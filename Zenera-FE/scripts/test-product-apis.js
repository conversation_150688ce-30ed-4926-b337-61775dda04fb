/**
 * <PERSON><PERSON>t để kiểm thử các API sản phẩm và kho hàng
 * 
 * <PERSON><PERSON>ch sử dụng:
 * 1. Đảm bảo server đ<PERSON> chạy (pnpm run dev)
 * 2. Chạy script: node scripts/test-product-apis.js
 */

const fetch = require('node-fetch');

// C<PERSON><PERSON> hình
const API_BASE_URL = 'http://localhost:3001/api';
let authToken = '';
let productId = '';
let reviewId = '';

// Hàm helper để gọi API
async function callApi(endpoint, method = 'GET', body = null) {
  const headers = {
    'Content-Type': 'application/json',
  };

  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`;
  }

  const options = {
    method,
    headers,
  };

  if (body && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, options);
    const data = await response.json();
    
    return {
      status: response.status,
      data,
    };
  } catch (error) {
    console.error(`Error calling API ${endpoint}:`, error);
    return {
      status: 500,
      data: { error: error.message },
    };
  }
}

// Đăng nhập để lấy token
async function login() {
  console.log('\n--- Đăng nhập ---');
  
  const response = await callApi('/auth/login', 'POST', {
    email: '<EMAIL>',
    password: 'password123',
  });

  if (response.status === 200 && response.data.token) {
    authToken = response.data.token;
    console.log('✅ Đăng nhập thành công, đã lấy token');
    return true;
  } else {
    console.error('❌ Đăng nhập thất bại:', response.data);
    return false;
  }
}

// Tạo sản phẩm mới
async function createProduct() {
  console.log('\n--- Tạo sản phẩm mới ---');
  
  const product = {
    title: `Test Product ${Date.now()}`,
    description: 'This is a test product created by the API test script',
    price: 99.99,
    stock: 100,
    specifications: {
      'Color': 'Black',
      'Weight': '200g',
      'Dimensions': '10 x 5 x 2 cm'
    },
    minStockThreshold: 10
  };

  const response = await callApi('/seller/products', 'POST', product);

  if (response.status === 201 && response.data._id) {
    productId = response.data._id;
    console.log(`✅ Tạo sản phẩm thành công, ID: ${productId}`);
    console.log('Thông tin sản phẩm:', response.data);
    return true;
  } else {
    console.error('❌ Tạo sản phẩm thất bại:', response.data);
    return false;
  }
}

// Nhân bản sản phẩm
async function duplicateProduct() {
  if (!productId) {
    console.error('❌ Không có ID sản phẩm để nhân bản');
    return false;
  }

  console.log('\n--- Nhân bản sản phẩm ---');
  
  const response = await callApi(`/seller/products/${productId}/duplicate`, 'POST');

  if (response.status === 201 && response.data._id) {
    console.log(`✅ Nhân bản sản phẩm thành công, ID mới: ${response.data._id}`);
    console.log('Thông tin sản phẩm nhân bản:', response.data);
    return true;
  } else {
    console.error('❌ Nhân bản sản phẩm thất bại:', response.data);
    return false;
  }
}

// Thêm hình ảnh sản phẩm
async function addProductImage() {
  if (!productId) {
    console.error('❌ Không có ID sản phẩm để thêm hình ảnh');
    return false;
  }

  console.log('\n--- Thêm hình ảnh sản phẩm ---');
  
  const response = await callApi(`/seller/products/${productId}/images`, 'POST', {
    imageUrl: 'https://example.com/image.jpg'
  });

  if (response.status === 200) {
    console.log('✅ Thêm hình ảnh sản phẩm thành công');
    console.log('Danh sách hình ảnh:', response.data.images);
    return true;
  } else {
    console.error('❌ Thêm hình ảnh sản phẩm thất bại:', response.data);
    return false;
  }
}

// Cập nhật tồn kho hàng loạt
async function batchUpdateInventory() {
  if (!productId) {
    console.error('❌ Không có ID sản phẩm để cập nhật tồn kho');
    return false;
  }

  console.log('\n--- Cập nhật tồn kho hàng loạt ---');
  
  const response = await callApi('/seller/inventory/batch', 'POST', {
    items: [
      {
        productId,
        quantity: 10,
        actionType: 'add',
        notes: 'Thêm hàng từ script test'
      }
    ]
  });

  if (response.status === 200 && response.data.success) {
    console.log('✅ Cập nhật tồn kho hàng loạt thành công');
    console.log('Kết quả:', response.data.results);
    return true;
  } else {
    console.error('❌ Cập nhật tồn kho hàng loạt thất bại:', response.data);
    return false;
  }
}

// Cài đặt ngưỡng tồn kho tối thiểu
async function setStockThreshold() {
  if (!productId) {
    console.error('❌ Không có ID sản phẩm để cài đặt ngưỡng tồn kho');
    return false;
  }

  console.log('\n--- Cài đặt ngưỡng tồn kho tối thiểu ---');
  
  const response = await callApi(`/seller/products/${productId}/threshold`, 'PUT', {
    minStockThreshold: 15
  });

  if (response.status === 200 && response.data.success) {
    console.log('✅ Cài đặt ngưỡng tồn kho tối thiểu thành công');
    console.log('Thông tin ngưỡng:', response.data);
    return true;
  } else {
    console.error('❌ Cài đặt ngưỡng tồn kho tối thiểu thất bại:', response.data);
    return false;
  }
}

// Thêm đánh giá sản phẩm
async function addProductReview() {
  if (!productId) {
    console.error('❌ Không có ID sản phẩm để thêm đánh giá');
    return false;
  }

  console.log('\n--- Thêm đánh giá sản phẩm ---');
  
  const response = await callApi(`/products/${productId}/reviews`, 'POST', {
    rating: 5,
    comment: 'Sản phẩm rất tốt, đánh giá từ script test'
  });

  if (response.status === 201 && response.data._id) {
    reviewId = response.data._id;
    console.log(`✅ Thêm đánh giá sản phẩm thành công, ID: ${reviewId}`);
    console.log('Thông tin đánh giá:', response.data);
    return true;
  } else {
    console.error('❌ Thêm đánh giá sản phẩm thất bại:', response.data);
    return false;
  }
}

// Cập nhật đánh giá sản phẩm
async function updateProductReview() {
  if (!productId || !reviewId) {
    console.error('❌ Không có ID sản phẩm hoặc ID đánh giá để cập nhật');
    return false;
  }

  console.log('\n--- Cập nhật đánh giá sản phẩm ---');
  
  const response = await callApi(`/products/${productId}/reviews/${reviewId}`, 'PUT', {
    rating: 4,
    comment: 'Sản phẩm khá tốt, đã cập nhật đánh giá từ script test'
  });

  if (response.status === 200) {
    console.log('✅ Cập nhật đánh giá sản phẩm thành công');
    console.log('Thông tin đánh giá sau khi cập nhật:', response.data);
    return true;
  } else {
    console.error('❌ Cập nhật đánh giá sản phẩm thất bại:', response.data);
    return false;
  }
}

// Chạy tất cả các test
async function runTests() {
  console.log('=== BẮT ĐẦU KIỂM THỬ API SẢN PHẨM VÀ KHO HÀNG ===');
  
  // Đăng nhập
  if (!await login()) return;
  
  // Tạo sản phẩm mới
  if (!await createProduct()) return;
  
  // Nhân bản sản phẩm
  await duplicateProduct();
  
  // Thêm hình ảnh sản phẩm
  await addProductImage();
  
  // Cập nhật tồn kho hàng loạt
  await batchUpdateInventory();
  
  // Cài đặt ngưỡng tồn kho tối thiểu
  await setStockThreshold();
  
  // Thêm đánh giá sản phẩm
  await addProductReview();
  
  // Cập nhật đánh giá sản phẩm
  await updateProductReview();
  
  console.log('\n=== KẾT THÚC KIỂM THỬ API SẢN PHẨM VÀ KHO HÀNG ===');
}

// Chạy các test
runTests();
