// Script to seed rich data with Unsplash images
require('dotenv').config({ path: '.env.local' });

const { MongoClient, ObjectId } = require('mongodb');
const fetch = require('node-fetch');
const slugify = require('slugify');
const { faker } = require('@faker-js/faker');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'your_uri';
const MONGODB_DB = process.env.MONGODB_DB || 'ZenBuy';

// Unsplash API
const UNSPLASH_ACCESS_KEY = process.env.UNSPLASH_ACCESS_KEY;
const UNSPLASH_API_URL = 'https://api.unsplash.com';

// Configuration
const NUM_USERS = 20;
const NUM_SHOPS = 10;
const NUM_CATEGORIES = 15;
const NUM_PRODUCTS = 200;

// Category definitions with corresponding Unsplash search terms
const CATEGORY_DEFINITIONS = [
  { name: 'Electronics', searchTerm: 'electronics', description: 'Latest gadgets and electronic devices' },
  { name: 'Smartphones', searchTerm: 'smartphone', description: 'Latest smartphones and accessories', parent: 'Electronics' },
  { name: 'Laptops', searchTerm: 'laptop', description: 'Powerful laptops for work and gaming', parent: 'Electronics' },
  { name: 'Audio', searchTerm: 'headphones', description: 'High-quality audio equipment', parent: 'Electronics' },
  { name: 'Clothing', searchTerm: 'clothing', description: 'Trendy clothing for all seasons' },
  { name: 'Men\'s Fashion', searchTerm: 'men fashion', description: 'Stylish clothing for men', parent: 'Clothing' },
  { name: 'Women\'s Fashion', searchTerm: 'women fashion', description: 'Elegant clothing for women', parent: 'Clothing' },
  { name: 'Shoes', searchTerm: 'shoes', description: 'Comfortable and stylish footwear', parent: 'Clothing' },
  { name: 'Home & Kitchen', searchTerm: 'home kitchen', description: 'Everything for your home and kitchen' },
  { name: 'Furniture', searchTerm: 'furniture', description: 'Modern and classic furniture', parent: 'Home & Kitchen' },
  { name: 'Kitchenware', searchTerm: 'kitchenware', description: 'High-quality kitchen tools and appliances', parent: 'Home & Kitchen' },
  { name: 'Books', searchTerm: 'books', description: 'Books of all genres' },
  { name: 'Fiction', searchTerm: 'fiction books', description: 'Fiction books for all ages', parent: 'Books' },
  { name: 'Non-Fiction', searchTerm: 'non-fiction books', description: 'Educational and informative books', parent: 'Books' },
  { name: 'Sports & Outdoors', searchTerm: 'sports', description: 'Equipment for sports and outdoor activities' },
  { name: 'Beauty & Personal Care', searchTerm: 'beauty products', description: 'Beauty and personal care products' },
  { name: 'Toys & Games', searchTerm: 'toys', description: 'Toys and games for all ages' },
  { name: 'Jewelry', searchTerm: 'jewelry', description: 'Elegant jewelry and accessories' },
  { name: 'Health & Wellness', searchTerm: 'wellness', description: 'Products for a healthy lifestyle' },
  { name: 'Automotive', searchTerm: 'automotive', description: 'Automotive parts and accessories' }
];

// Shop types with corresponding Unsplash search terms for logos and banners
const SHOP_TYPES = [
  { type: 'Electronics Store', logoTerm: 'tech logo', bannerTerm: 'electronics store' },
  { type: 'Fashion Boutique', logoTerm: 'fashion logo', bannerTerm: 'clothing store' },
  { type: 'Home Goods', logoTerm: 'home logo', bannerTerm: 'home store' },
  { type: 'Bookstore', logoTerm: 'book logo', bannerTerm: 'bookstore' },
  { type: 'Sports Shop', logoTerm: 'sports logo', bannerTerm: 'sports store' },
  { type: 'Beauty Store', logoTerm: 'beauty logo', bannerTerm: 'beauty store' },
  { type: 'Toy Store', logoTerm: 'toy logo', bannerTerm: 'toy store' },
  { type: 'Jewelry Shop', logoTerm: 'jewelry logo', bannerTerm: 'jewelry store' },
  { type: 'Health Store', logoTerm: 'health logo', bannerTerm: 'health store' },
  { type: 'Auto Parts', logoTerm: 'auto logo', bannerTerm: 'auto store' }
];

// Product attribute definitions by category
const PRODUCT_ATTRIBUTES = {
  'Electronics': {
    variants: [
      { name: 'Storage', values: ['64GB', '128GB', '256GB', '512GB', '1TB'] },
      { name: 'Color', values: ['Black', 'White', 'Silver', 'Gold', 'Blue'] },
      { name: 'RAM', values: ['4GB', '8GB', '16GB', '32GB'] }
    ]
  },
  'Smartphones': {
    variants: [
      { name: 'Storage', values: ['64GB', '128GB', '256GB', '512GB'] },
      { name: 'Color', values: ['Black', 'White', 'Silver', 'Gold', 'Blue', 'Red'] },
      { name: 'RAM', values: ['4GB', '6GB', '8GB', '12GB'] }
    ]
  },
  'Laptops': {
    variants: [
      { name: 'Storage', values: ['256GB SSD', '512GB SSD', '1TB SSD', '2TB SSD'] },
      { name: 'Color', values: ['Black', 'Silver', 'Space Gray'] },
      { name: 'RAM', values: ['8GB', '16GB', '32GB', '64GB'] },
      { name: 'Processor', values: ['Intel i5', 'Intel i7', 'Intel i9', 'AMD Ryzen 5', 'AMD Ryzen 7', 'AMD Ryzen 9'] }
    ]
  },
  'Clothing': {
    variants: [
      { name: 'Size', values: ['XS', 'S', 'M', 'L', 'XL', 'XXL'] },
      { name: 'Color', values: ['Black', 'White', 'Red', 'Blue', 'Green', 'Yellow', 'Purple', 'Pink', 'Gray'] }
    ]
  },
  'Men\'s Fashion': {
    variants: [
      { name: 'Size', values: ['S', 'M', 'L', 'XL', 'XXL'] },
      { name: 'Color', values: ['Black', 'White', 'Blue', 'Gray', 'Brown', 'Navy'] }
    ]
  },
  'Women\'s Fashion': {
    variants: [
      { name: 'Size', values: ['XS', 'S', 'M', 'L', 'XL'] },
      { name: 'Color', values: ['Black', 'White', 'Red', 'Blue', 'Green', 'Pink', 'Purple'] }
    ]
  },
  'Shoes': {
    variants: [
      { name: 'Size', values: ['36', '37', '38', '39', '40', '41', '42', '43', '44', '45'] },
      { name: 'Color', values: ['Black', 'White', 'Brown', 'Blue', 'Red'] }
    ]
  },
  'default': {
    variants: [
      { name: 'Size', values: ['Small', 'Medium', 'Large'] },
      { name: 'Color', values: ['Black', 'White', 'Multi'] }
    ]
  }
};

// Fallback image collections for different categories
const FALLBACK_IMAGES = {
  // Electronics
  'electronics': [
    'https://images.pexels.com/photos/1029757/pexels-photo-1029757.jpeg',
    'https://images.pexels.com/photos/792345/pexels-photo-792345.jpeg',
    'https://images.pexels.com/photos/1293120/pexels-photo-1293120.jpeg',
    'https://images.pexels.com/photos/1841841/pexels-photo-1841841.jpeg',
    'https://images.pexels.com/photos/343457/pexels-photo-343457.jpeg'
  ],
  'smartphone': [
    'https://images.pexels.com/photos/47261/pexels-photo-47261.jpeg',
    'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg',
    'https://images.pexels.com/photos/607812/pexels-photo-607812.jpeg',
    'https://images.pexels.com/photos/1092644/pexels-photo-1092644.jpeg',
    'https://images.pexels.com/photos/404280/pexels-photo-404280.jpeg'
  ],
  'laptop': [
    'https://images.pexels.com/photos/18105/pexels-photo.jpg',
    'https://images.pexels.com/photos/303383/pexels-photo-303383.jpeg',
    'https://images.pexels.com/photos/1229861/pexels-photo-1229861.jpeg',
    'https://images.pexels.com/photos/748777/pexels-photo-748777.jpeg',
    'https://images.pexels.com/photos/1181675/pexels-photo-1181675.jpeg'
  ],
  // Clothing
  'clothing': [
    'https://images.pexels.com/photos/934063/pexels-photo-934063.jpeg',
    'https://images.pexels.com/photos/1884581/pexels-photo-1884581.jpeg',
    'https://images.pexels.com/photos/1078958/pexels-photo-1078958.jpeg',
    'https://images.pexels.com/photos/325876/pexels-photo-325876.jpeg',
    'https://images.pexels.com/photos/996329/pexels-photo-996329.jpeg'
  ],
  'men fashion': [
    'https://images.pexels.com/photos/1342609/pexels-photo-1342609.jpeg',
    'https://images.pexels.com/photos/1049317/pexels-photo-1049317.jpeg',
    'https://images.pexels.com/photos/1300550/pexels-photo-1300550.jpeg',
    'https://images.pexels.com/photos/2897531/pexels-photo-2897531.jpeg',
    'https://images.pexels.com/photos/1192609/pexels-photo-1192609.jpeg'
  ],
  'women fashion': [
    'https://images.pexels.com/photos/972995/pexels-photo-972995.jpeg',
    'https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg',
    'https://images.pexels.com/photos/1536619/pexels-photo-1536619.jpeg',
    'https://images.pexels.com/photos/1462637/pexels-photo-1462637.jpeg',
    'https://images.pexels.com/photos/1375736/pexels-photo-1375736.jpeg'
  ],
  // Home & Kitchen
  'home kitchen': [
    'https://images.pexels.com/photos/1080721/pexels-photo-1080721.jpeg',
    'https://images.pexels.com/photos/1358900/pexels-photo-1358900.jpeg',
    'https://images.pexels.com/photos/534151/pexels-photo-534151.jpeg',
    'https://images.pexels.com/photos/2724749/pexels-photo-2724749.jpeg',
    'https://images.pexels.com/photos/2062426/pexels-photo-2062426.jpeg'
  ],
  // Books
  'books': [
    'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg',
    'https://images.pexels.com/photos/1370295/pexels-photo-1370295.jpeg',
    'https://images.pexels.com/photos/590493/pexels-photo-590493.jpeg',
    'https://images.pexels.com/photos/694740/pexels-photo-694740.jpeg',
    'https://images.pexels.com/photos/1370298/pexels-photo-1370298.jpeg'
  ],
  // Default
  'default': [
    'https://images.pexels.com/photos/1667088/pexels-photo-1667088.jpeg',
    'https://images.pexels.com/photos/2292953/pexels-photo-2292953.jpeg',
    'https://images.pexels.com/photos/1303081/pexels-photo-1303081.jpeg',
    'https://images.pexels.com/photos/1038000/pexels-photo-1038000.jpeg',
    'https://images.pexels.com/photos/1038001/pexels-photo-1038001.jpeg'
  ]
};

// Helper function to get fallback images for a category
function getFallbackImages(query, count = 1) {
  // Find the closest matching category
  const category = Object.keys(FALLBACK_IMAGES).find(key =>
    query.toLowerCase().includes(key.toLowerCase())
  ) || 'default';

  const images = FALLBACK_IMAGES[category] || FALLBACK_IMAGES.default;

  // Return random images from the category
  return Array.from({ length: count }, () =>
    images[Math.floor(Math.random() * images.length)]
  );
}

// Helper function to fetch images from Unsplash with retry
async function fetchUnsplashImages(query, count = 1, useUnsplash = true) {
  // Skip Unsplash if disabled
  if (!useUnsplash) {
    return getFallbackImages(query, count);
  }

  // Maximum retry attempts
  const MAX_RETRIES = 3;
  let retries = 0;

  while (retries < MAX_RETRIES) {
    try {
      const response = await fetch(
        `${UNSPLASH_API_URL}/search/photos?query=${encodeURIComponent(query)}&per_page=${count}&client_id=${UNSPLASH_ACCESS_KEY}`,
        { timeout: 5000 } // 5 second timeout
      );

      if (!response.ok) {
        throw new Error(`Unsplash API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      if (data.results && data.results.length > 0) {
        return data.results.map(result => result.urls.regular);
      } else {
        console.log(`No images found for "${query}", using fallback images`);
        return getFallbackImages(query, count);
      }
    } catch (error) {
      retries++;
      console.error(`Error fetching images for "${query}" (attempt ${retries}/${MAX_RETRIES}):`, error);

      if (retries >= MAX_RETRIES) {
        console.log(`Max retries reached for "${query}", using fallback images`);
        return getFallbackImages(query, count);
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, 1000 * retries));
    }
  }

  // Fallback if all retries fail
  return getFallbackImages(query, count);
}

// Helper function to create a slug
function createSlug(text) {
  return slugify(text, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });
}

// Helper function to get random element from array
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Helper function to get random elements from array
function getRandomElements(array, count) {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// Helper function to generate product variants
function generateProductVariants(category, basePrice) {
  const categoryAttrs = PRODUCT_ATTRIBUTES[category] || PRODUCT_ATTRIBUTES.default;
  const numVariants = Math.floor(Math.random() * 5) + 1; // 1-5 variants
  const variants = [];

  for (let i = 0; i < numVariants; i++) {
    const attributes = {};
    let variantName = '';

    // Add random attributes
    categoryAttrs.variants.forEach(attr => {
      const value = getRandomElement(attr.values);
      attributes[attr.name] = value;
      variantName += `${value} / `;
    });

    variantName = variantName.slice(0, -3); // Remove trailing " / "

    // Price variation based on attributes
    let priceMultiplier = 1.0;
    if (attributes.Storage && attributes.Storage.includes('TB')) priceMultiplier += 0.5;
    if (attributes.RAM && parseInt(attributes.RAM) > 16) priceMultiplier += 0.3;

    variants.push({
      _id: new ObjectId(),
      name: variantName,
      price: Math.round(basePrice * priceMultiplier * 100) / 100,
      stock: Math.floor(Math.random() * 100) + 1,
      attributes
    });
  }

  return variants;
}

// Function to seed users
async function seedUsers(context) {
  const { db, force = false } = context;
  console.log('Creating users...');

  // Check if users already exist
  const existingUsers = await db.collection('users').countDocuments();
  if (existingUsers > 0 && !force) {
    console.log(`Users collection already has ${existingUsers} documents. Use --force to overwrite.`);
    return;
  }

  const users = [];
  const userAvatars = await fetchUnsplashImages('person portrait', NUM_USERS);

  for (let i = 0; i < NUM_USERS; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const name = `${firstName} ${lastName}`;
    const email = faker.internet.email({ firstName, lastName }).toLowerCase();

    users.push({
      name,
      email,
      password: '$2a$10$XHrO9cX.2/aTJ0Ld4Gy7/.SmUxCWXLHxmaMTQxZUQz2niTWwRQjxu', // 'password123'
      role: i < 10 ? 'seller' : 'customer',
      avatar: userAvatars[i] || faker.image.avatar(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent()
    });
  }

  // Add an admin user
  users.push({
    name: 'Admin User',
    email: '<EMAIL>',
    password: '$2a$10$XHrO9cX.2/aTJ0Ld4Gy7/.SmUxCWXLHxmaMTQxZUQz2niTWwRQjxu', // 'password123'
    role: 'admin',
    avatar: faker.image.avatar(),
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent()
  });

  if (force) {
    await db.collection('users').deleteMany({});
  }

  const userResult = await db.collection('users').insertMany(users);
  console.log(`${users.length} users created`);

  return userResult;
}

// Function to seed shops
async function seedShops(context) {
  const { db, force = false } = context;
  console.log('Creating shops...');

  // Check if shops already exist
  const existingShops = await db.collection('shops').countDocuments();
  if (existingShops > 0 && !force) {
    console.log(`Shops collection already has ${existingShops} documents. Use --force to overwrite.`);
    return;
  }

  // Get users for shop owners
  const sellerUsers = await db.collection('users').find({ role: 'seller' }).toArray();
  if (sellerUsers.length === 0) {
    console.log('No seller users found. Please seed users first.');
    return;
  }

  const shops = [];

  for (let i = 0; i < Math.min(NUM_SHOPS, sellerUsers.length); i++) {
    const shopType = SHOP_TYPES[i % SHOP_TYPES.length];
    const shopName = `${faker.company.name()} ${shopType.type}`;
    const shopSlug = createSlug(shopName);

    // Fetch logo and banner from Unsplash
    const [logoImages, bannerImages] = await Promise.all([
      fetchUnsplashImages(shopType.logoTerm, 1),
      fetchUnsplashImages(shopType.bannerTerm, 1)
    ]);

    shops.push({
      name: shopName,
      slug: shopSlug,
      description: faker.company.catchPhrase(),
      logo: logoImages[0],
      banner: bannerImages[0],
      owner: sellerUsers[i]._id,
      followers: faker.number.int({ min: 10, max: 5000 }),
      rating: faker.number.float({ min: 3.0, max: 5.0, precision: 0.1 }),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent()
    });
  }

  if (force) {
    await db.collection('shops').deleteMany({});
  }

  const shopResult = await db.collection('shops').insertMany(shops);
  console.log(`${shops.length} shops created`);

  return shopResult;
}

// Function to seed categories
async function seedCategories(context) {
  const { db, force = false } = context;
  console.log('Creating categories...');

  // Check if categories already exist
  const existingCategories = await db.collection('categories').countDocuments();
  if (existingCategories > 0 && !force) {
    console.log(`Categories collection already has ${existingCategories} documents. Use --force to overwrite.`);
    return;
  }

  const categories = [];
  const categoryMap = {};

  // First pass: create main categories
  for (const catDef of CATEGORY_DEFINITIONS) {
    if (!catDef.parent) {
      const categoryImage = await fetchUnsplashImages(catDef.searchTerm, 1);

      const category = {
        name: catDef.name,
        slug: createSlug(catDef.name),
        description: catDef.description,
        image: categoryImage[0],
        createdAt: faker.date.past(),
        updatedAt: faker.date.recent()
      };

      categories.push(category);
      categoryMap[catDef.name] = categories.length - 1;
    }
  }

  // Second pass: create subcategories
  for (const catDef of CATEGORY_DEFINITIONS) {
    if (catDef.parent && categoryMap[catDef.parent] !== undefined) {
      const categoryImage = await fetchUnsplashImages(catDef.searchTerm, 1);

      const category = {
        name: catDef.name,
        slug: createSlug(catDef.name),
        description: catDef.description,
        image: categoryImage[0],
        parent: categories[categoryMap[catDef.parent]]._id,
        createdAt: faker.date.past(),
        updatedAt: faker.date.recent()
      };

      categories.push(category);
      categoryMap[catDef.name] = categories.length - 1;
    }
  }

  // Assign ObjectIds to categories
  categories.forEach(category => {
    category._id = new ObjectId();
  });

  // Update parent references with actual ObjectIds
  categories.forEach(category => {
    if (category.parent) {
      const parentIndex = categories.findIndex(c => c.name === CATEGORY_DEFINITIONS.find(cd => cd.name === category.name).parent);
      if (parentIndex !== -1) {
        category.parent = categories[parentIndex]._id;
      }
    }
  });

  if (force) {
    await db.collection('categories').deleteMany({});
  }

  await db.collection('categories').insertMany(categories);
  console.log(`${categories.length} categories created`);

  return categories;
}

// Function to seed products
async function seedProducts(context) {
  const { db, force = false, useUnsplash = true, batchSize = 20 } = context;
  console.log('Creating products...');

  // Check if products already exist
  const existingProducts = await db.collection('products').countDocuments();
  if (existingProducts > 0 && !force) {
    console.log(`Products collection already has ${existingProducts} documents. Use --force to overwrite.`);
    return;
  }

  // Get categories and shops
  const categories = await db.collection('categories').find().toArray();
  if (categories.length === 0) {
    console.log('No categories found. Please seed categories first.');
    return;
  }

  const shops = await db.collection('shops').find().toArray();
  if (shops.length === 0) {
    console.log('No shops found. Please seed shops first.');
    return;
  }

  // If force is true, clear the products collection
  if (force) {
    console.log('Clearing existing products...');
    await db.collection('products').deleteMany({});
  }

  // Create products in batches to avoid memory issues
  const totalProducts = NUM_PRODUCTS;
  const batches = Math.ceil(totalProducts / batchSize);

  console.log(`Creating ${totalProducts} products in ${batches} batches of ${batchSize}...`);
  console.log(`Using ${useUnsplash ? 'Unsplash API' : 'local fallback images'} for product images`);

  let totalCreated = 0;

  for (let batch = 0; batch < batches; batch++) {
    const products = [];
    const batchStart = batch * batchSize;
    const batchEnd = Math.min(batchStart + batchSize, totalProducts);
    const currentBatchSize = batchEnd - batchStart;

    console.log(`Processing batch ${batch + 1}/${batches} (products ${batchStart + 1}-${batchEnd})...`);

    for (let i = 0; i < currentBatchSize; i++) {
      // Select a random category and shop
      const category = getRandomElement(categories);
      const shop = getRandomElement(shops);

      // Find the category definition to get the search term
      const categoryDef = CATEGORY_DEFINITIONS.find(cd => cd.name === category.name);
      const searchTerm = categoryDef ? categoryDef.searchTerm : 'product';

      // Fetch product images (3-5 images per product)
      const numImages = Math.floor(Math.random() * 3) + 3; // 3-5 images
      const productImages = await fetchUnsplashImages(searchTerm, numImages, useUnsplash);

      // Generate product title and description
      const productTitle = faker.commerce.productName();
      const productDescription = faker.commerce.productDescription() + '\n\n' +
        Array(Math.floor(Math.random() * 3) + 2).fill(0).map(() =>
          '• ' + faker.commerce.productAdjective() + ' ' + faker.commerce.productMaterial()
        ).join('\n');

      // Generate price and discount
      const basePrice = faker.number.float({ min: 10, max: 1000, precision: 0.01 });
      const hasDiscount = Math.random() > 0.7; // 30% chance of having a discount
      const discountPrice = hasDiscount ? basePrice * (1 - faker.number.float({ min: 0.1, max: 0.5, precision: 0.01 })) : null;

      // Generate variants
      const variants = generateProductVariants(category.name, basePrice);

      products.push({
        title: productTitle,
        slug: createSlug(productTitle) + '-' + faker.string.alphanumeric(6).toLowerCase(),
        description: productDescription,
        price: basePrice,
        discountPrice: discountPrice,
        stock: Math.floor(Math.random() * 100) + 1,
        images: productImages,
        category: category._id,
        shop: shop._id,
        rating: faker.number.float({ min: 1, max: 5, precision: 0.1 }),
        reviews: Math.floor(Math.random() * 500),
        featured: Math.random() > 0.8, // 20% chance of being featured
        variants: variants,
        createdAt: faker.date.past(),
        updatedAt: faker.date.recent()
      });
    }

    // Insert batch of products
    if (products.length > 0) {
      await db.collection('products').insertMany(products);
      totalCreated += products.length;
      console.log(`Batch ${batch + 1}/${batches} completed. Total products created: ${totalCreated}/${totalProducts}`);
    }
  }

  console.log(`${totalCreated} products created successfully`);
  return totalCreated;
}

// Main function to seed all data
async function seedData() {
  console.log('Starting to seed rich data...');

  // Connect to MongoDB
  const client = new MongoClient(MONGODB_URI);
  await client.connect();
  console.log('Connected to MongoDB');

  const db = client.db(MONGODB_DB);

  try {
    const context = {
      db,
      force: true,
      useUnsplash: true,
      batchSize: 20
    };
    // Seed all collections
    await seedUsers(context);
    await seedShops(context);
    await seedCategories(context);
    await seedProducts(context);

    console.log('Rich data seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding data:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Export functions for use in other scripts
module.exports = {
  seedUsers,
  seedShops,
  seedCategories,
  seedProducts,
  seedData
};

// Run the seed function
seedData().catch(console.error);
