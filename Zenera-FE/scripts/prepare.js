// This script is used to conditionally install husky hooks
// It will only run husky install if we're not in a CI environment

const { execSync } = require('child_process');

// Check if we're in a CI environment
const isCI = process.env.CI === 'true' || process.env.VERCEL === '1';

if (!isCI) {
  console.log('Setting up husky hooks...');
  try {
    execSync('npx husky install', { stdio: 'inherit' });
    console.log('Husky hooks installed successfully');
  } catch (error) {
    console.error('Failed to install husky hooks:', error);
  }
} else {
  console.log('Skipping husky installation in CI environment');
}
