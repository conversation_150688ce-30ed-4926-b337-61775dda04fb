import nodemailer from 'nodemailer';
import { Order } from '@/lib/models';

// Cấu hình transporter cho nodemailer
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Kiểm tra cấu hình email
export const verifyEmailConfig = async () => {
  try {
    await transporter.verify();
    return true;
  } catch (error) {
    console.error('Email configuration error:', error);
    return false;
  }
};

// Gửi email xác nhận đơn hàng
export const sendOrderConfirmationEmail = async (order: any) => {
  try {
    // Kiểm tra cấu hình email
    const isConfigValid = await verifyEmailConfig();
    if (!isConfigValid) {
      console.error('Email configuration is invalid');
      return false;
    }

    // Tạo nội dung email
    const items = order.items.map((item: any) => `
      <tr>
        <td style="padding: 10px; border-bottom: 1px solid #eee;">${item.product.title}</td>
        <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
        <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(item.price)}</td>
        <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(item.price * item.quantity)}</td>
      </tr>
    `).join('');

    const emailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
        <h2 style="color: #333; text-align: center;">Xác nhận đơn hàng</h2>
        <p>Xin chào ${order.shippingAddress.fullName},</p>
        <p>Cảm ơn bạn đã đặt hàng tại ZenBuy. Đơn hàng của bạn đã được xác nhận và đang được xử lý.</p>
        
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Thông tin đơn hàng</h3>
          <p><strong>Mã đơn hàng:</strong> ${order.orderNumber}</p>
          <p><strong>Ngày đặt hàng:</strong> ${new Date(order.createdAt).toLocaleDateString('vi-VN')}</p>
          <p><strong>Trạng thái:</strong> ${order.status === 'pending' ? 'Chờ xử lý' : order.status === 'processing' ? 'Đang xử lý' : order.status === 'shipped' ? 'Đang giao hàng' : order.status === 'delivered' ? 'Đã giao hàng' : 'Đã hủy'}</p>
          <p><strong>Phương thức thanh toán:</strong> ${order.paymentMethod === 'credit_card' ? 'Thẻ tín dụng' : order.paymentMethod === 'bank_transfer' ? 'Chuyển khoản ngân hàng' : 'Thanh toán khi nhận hàng'}</p>
        </div>
        
        <div style="margin: 20px 0;">
          <h3>Chi tiết đơn hàng</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr style="background-color: #f2f2f2;">
                <th style="padding: 10px; text-align: left;">Sản phẩm</th>
                <th style="padding: 10px; text-align: center;">Số lượng</th>
                <th style="padding: 10px; text-align: right;">Đơn giá</th>
                <th style="padding: 10px; text-align: right;">Thành tiền</th>
              </tr>
            </thead>
            <tbody>
              ${items}
            </tbody>
            <tfoot>
              <tr>
                <td colspan="3" style="padding: 10px; text-align: right;"><strong>Tạm tính:</strong></td>
                <td style="padding: 10px; text-align: right;">${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(order.totalAmount)}</td>
              </tr>
              <tr>
                <td colspan="3" style="padding: 10px; text-align: right;"><strong>Phí vận chuyển:</strong></td>
                <td style="padding: 10px; text-align: right;">${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(order.shippingFee)}</td>
              </tr>
              <tr>
                <td colspan="3" style="padding: 10px; text-align: right;"><strong>Thuế:</strong></td>
                <td style="padding: 10px; text-align: right;">${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(order.tax)}</td>
              </tr>
              <tr>
                <td colspan="3" style="padding: 10px; text-align: right;"><strong>Tổng cộng:</strong></td>
                <td style="padding: 10px; text-align: right; font-weight: bold;">${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(order.finalAmount)}</td>
              </tr>
            </tfoot>
          </table>
        </div>
        
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Địa chỉ giao hàng</h3>
          <p>${order.shippingAddress.fullName}</p>
          <p>${order.shippingAddress.address}</p>
          <p>${order.shippingAddress.city}, ${order.shippingAddress.state}, ${order.shippingAddress.country}</p>
          <p>${order.shippingAddress.zipCode}</p>
          <p>Điện thoại: ${order.shippingAddress.phone}</p>
        </div>
        
        <p>Nếu bạn có bất kỳ câu hỏi nào về đơn hàng, vui lòng liên hệ với chúng tôi <NAME_EMAIL> hoặc số điện thoại 1900 1234.</p>
        
        <p>Trân trọng,</p>
        <p><strong>Đội ngũ ZenBuy</strong></p>
      </div>
    `;

    // Gửi email
    const info = await transporter.sendMail({
      from: `"ZenBuy" <${process.env.EMAIL_USER}>`,
      to: order.user.email,
      subject: `Xác nhận đơn hàng #${order.orderNumber}`,
      html: emailContent,
    });

    console.log('Order confirmation email sent:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending order confirmation email:', error);
    return false;
  }
};

// Gửi email cập nhật trạng thái đơn hàng
export const sendOrderStatusUpdateEmail = async (order: any, previousStatus: string) => {
  try {
    // Kiểm tra cấu hình email
    const isConfigValid = await verifyEmailConfig();
    if (!isConfigValid) {
      console.error('Email configuration is invalid');
      return false;
    }

    // Tạo nội dung email
    const statusText = order.status === 'pending' ? 'Chờ xử lý' : 
                       order.status === 'processing' ? 'Đang xử lý' : 
                       order.status === 'shipped' ? 'Đang giao hàng' : 
                       order.status === 'delivered' ? 'Đã giao hàng' : 'Đã hủy';
    
    const previousStatusText = previousStatus === 'pending' ? 'Chờ xử lý' : 
                              previousStatus === 'processing' ? 'Đang xử lý' : 
                              previousStatus === 'shipped' ? 'Đang giao hàng' : 
                              previousStatus === 'delivered' ? 'Đã giao hàng' : 'Đã hủy';

    const emailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
        <h2 style="color: #333; text-align: center;">Cập nhật trạng thái đơn hàng</h2>
        <p>Xin chào ${order.shippingAddress.fullName},</p>
        <p>Đơn hàng của bạn đã được cập nhật trạng thái từ <strong>${previousStatusText}</strong> thành <strong>${statusText}</strong>.</p>
        
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Thông tin đơn hàng</h3>
          <p><strong>Mã đơn hàng:</strong> ${order.orderNumber}</p>
          <p><strong>Ngày đặt hàng:</strong> ${new Date(order.createdAt).toLocaleDateString('vi-VN')}</p>
          <p><strong>Trạng thái mới:</strong> ${statusText}</p>
        </div>
        
        <p>Bạn có thể theo dõi đơn hàng của mình tại <a href="${process.env.NEXT_PUBLIC_API_URL}/orders/${order._id}">đây</a>.</p>
        
        <p>Nếu bạn có bất kỳ câu hỏi nào về đơn hàng, vui lòng liên hệ với chúng tôi <NAME_EMAIL> hoặc số điện thoại 1900 1234.</p>
        
        <p>Trân trọng,</p>
        <p><strong>Đội ngũ ZenBuy</strong></p>
      </div>
    `;

    // Gửi email
    const info = await transporter.sendMail({
      from: `"ZenBuy" <${process.env.EMAIL_USER}>`,
      to: order.user.email,
      subject: `Cập nhật trạng thái đơn hàng #${order.orderNumber}`,
      html: emailContent,
    });

    console.log('Order status update email sent:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending order status update email:', error);
    return false;
  }
};
