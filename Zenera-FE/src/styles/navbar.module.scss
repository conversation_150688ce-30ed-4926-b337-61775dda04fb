// Navbar.module.scss

.navbar {
  background-color: var(--background);
  border-bottom: 1px solid var(--border);

  .container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;

    .sellerChannel {
      cursor: pointer;
      width: 100%;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--muted-foreground);

      &:hover {
        color: var(--primary);
      }
    }

    .mainNav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 4rem;

      .logo {
        cursor: pointer;
        flex-shrink: 0;
        margin-right: 1.25rem;

        img {
          border: 4px solid;
          border-image: linear-gradient(to right, var(--purple-400), var(--pink-500), var(--red-500)) 1;
          border-radius: 50%;
        }
      }

      .productsDropdown {
        position: relative;

        .dropdownButton {
          background: none;
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--muted-foreground);

          &:hover {
            color: var(--primary);
          }
        }

        .dropdownContent {
          background-color: #fff !important;
          z-index: 10;
          position: absolute;
          left: 0;
          top: 100%;
          margin-top: 0.5rem;
          width: 14rem;
          background-color: var(--background);
          border: 1px solid var(--border);
          border-radius: 0.375rem;
            box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.2);
          transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
          opacity: 0;
          visibility: hidden;

          &.open {
            opacity: 1;
            visibility: visible;
          }

          .categoryItem {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;

            &:hover {
              background-color: var(--accent);
            }

            img {
              width: 2rem;
              height: 2rem;
              border-radius: 0.25rem;
            }
          }
        }
      }

      .searchBar {
        flex-grow: 1;
        margin: 0 1rem;
        
        .searchInput {
          position: relative;
          width: 100%;
          padding-left: 2.5rem;
        }

        .searchIcon {
          position: absolute;
          right: 5px !important;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
          color: var(--gray-400);
        }
      }

      .cartDropdown {
        position: relative;

        .cartButton {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--muted-foreground);

          &:hover {
            color: var(--primary);
          }
        }

        .cartContent {
          position: absolute;
          right: 0;
          top: 100%;
          margin-top: 0.5rem;
          width: 20rem;
          background-color: var(--background);
          border: 1px solid var(--border);
          border-radius: 0.375rem;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
          opacity: 0;
          visibility: hidden;

          &.open {
            opacity: 1;
            visibility: visible;
          }

          .cartItem {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.5rem 1rem;

            img {
              width: 2.5rem;
              height: 2.5rem;
              border-radius: 0.25rem;
            }

            .itemDetails {
              p {
                font-weight: 600;
              }

              .price {
                font-size: 0.875rem;
                color: var(--gray-500);
              }
            }
          }

          .viewCartButton {
            width: 100%;
            padding: 0.5rem 1rem;
          }
        }
      }

      .notificationButton {
        background: none;
        color: var(--muted-foreground);

        &:hover {
          color: var(--primary);
        }
      }
    }
  }
}