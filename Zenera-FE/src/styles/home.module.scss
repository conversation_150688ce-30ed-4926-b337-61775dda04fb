.heroSection {
  animation: fadeIn 2s ease-out;
}

.heroTitle {
  animation: slideInFromLeft 2s ease-out;
}

.heroDescription {
  animation: slideInFromRight 2s ease-out;
}

.heroButton {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.heroButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.categoryCard {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.categoryCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.productCard {
  transition: transform 0.3s ease;
}

.productCard:hover {
  transform: scale(1.05);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

