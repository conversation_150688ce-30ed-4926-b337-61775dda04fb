import { NextResponse } from 'next/server';
import { z } from 'zod';
import { isValidObjectId } from 'mongoose';
import dbConnect from '@/lib/mongodb';
import ShippingAddress from '@/models/ShippingAddress';
import { getAuthUser } from '@/lib/auth-utils';

// Schema xác thực cho cập nhật địa chỉ giao hàng
const updateShippingAddressSchema = z.object({
  fullName: z.string().min(2, 'Họ tên phải có ít nhất 2 ký tự').optional(),
  phoneNumber: z.string().min(10, 'Số điện thoại không hợp lệ').optional(),
  addressLine1: z.string().min(5, 'Địa chỉ phải có ít nhất 5 ký tự').optional(),
  addressLine2: z.string().optional(),
  city: z.string().min(2, 'Thành phố không hợp lệ').optional(),
  district: z.string().min(2, 'Quận/<PERSON>y<PERSON> không hợp lệ').optional(),
  ward: z.string().min(2, '<PERSON>ường/Xã không hợp lệ').optional(),
  postalCode: z.string().optional(),
  isDefault: z.boolean().optional(),
});

// Lấy thông tin chi tiết địa chỉ giao hàng
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const { id } = params;

    // Kiểm tra ID hợp lệ
    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'ID không hợp lệ' },
        { status: 400 }
      );
    }

    // Xác thực người dùng
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    // Lấy thông tin địa chỉ giao hàng
    const address = await ShippingAddress.findOne({
      _id: id,
      user: user._id,
    });

    if (!address) {
      return NextResponse.json(
        { error: 'Không tìm thấy địa chỉ giao hàng' },
        { status: 404 }
      );
    }

    return NextResponse.json(address);
  } catch (error) {
    console.error('Error fetching shipping address:', error);
    return NextResponse.json(
      { error: 'Lỗi máy chủ nội bộ' },
      { status: 500 }
    );
  }
}

// Cập nhật địa chỉ giao hàng
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const { id } = params;

    // Kiểm tra ID hợp lệ
    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'ID không hợp lệ' },
        { status: 400 }
      );
    }

    // Xác thực người dùng
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    // Xác thực dữ liệu đầu vào
    const body = await request.json();
    const validatedData = updateShippingAddressSchema.parse(body);

    // Kiểm tra địa chỉ tồn tại và thuộc về người dùng
    const address = await ShippingAddress.findOne({
      _id: id,
      user: user._id,
    });

    if (!address) {
      return NextResponse.json(
        { error: 'Không tìm thấy địa chỉ giao hàng' },
        { status: 404 }
      );
    }

    // Cập nhật địa chỉ giao hàng
    const updatedAddress = await ShippingAddress.findByIdAndUpdate(
      id,
      { $set: validatedData },
      { new: true }
    );

    return NextResponse.json(updatedAddress);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error('Error updating shipping address:', error);
    return NextResponse.json(
      { error: 'Lỗi máy chủ nội bộ' },
      { status: 500 }
    );
  }
}

// Xóa địa chỉ giao hàng
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const { id } = params;

    // Kiểm tra ID hợp lệ
    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'ID không hợp lệ' },
        { status: 400 }
      );
    }

    // Xác thực người dùng
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    // Kiểm tra địa chỉ tồn tại và thuộc về người dùng
    const address = await ShippingAddress.findOne({
      _id: id,
      user: user._id,
    });

    if (!address) {
      return NextResponse.json(
        { error: 'Không tìm thấy địa chỉ giao hàng' },
        { status: 404 }
      );
    }

    // Kiểm tra nếu đây là địa chỉ mặc định
    if (address.isDefault) {
      // Tìm địa chỉ khác để đặt làm mặc định
      const otherAddress = await ShippingAddress.findOne({
        user: user._id,
        _id: { $ne: id },
      });

      if (otherAddress) {
        otherAddress.isDefault = true;
        await otherAddress.save();
      }
    }

    // Xóa địa chỉ giao hàng
    await ShippingAddress.findByIdAndDelete(id);

    return NextResponse.json(
      { success: true, message: 'Đã xóa địa chỉ giao hàng' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting shipping address:', error);
    return NextResponse.json(
      { error: 'Lỗi máy chủ nội bộ' },
      { status: 500 }
    );
  }
}
