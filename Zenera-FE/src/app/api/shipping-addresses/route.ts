import { NextResponse } from 'next/server';
import { z } from 'zod';
import dbConnect from '@/lib/mongodb';
import ShippingAddress from '@/models/ShippingAddress';
import { getAuthUser } from '@/lib/auth-utils';

// Schema xác thực cho địa chỉ giao hàng
const shippingAddressSchema = z.object({
  fullName: z.string().min(2, 'Họ tên phải có ít nhất 2 ký tự'),
  phoneNumber: z.string().min(10, 'Số điện thoại không hợp lệ'),
  addressLine1: z.string().min(5, 'Địa chỉ phải có ít nhất 5 ký tự'),
  addressLine2: z.string().optional(),
  city: z.string().min(2, 'Thành phố không hợp lệ'),
  district: z.string().min(2, 'Quận/<PERSON>yện không hợp lệ'),
  ward: z.string().min(2, 'Phường/<PERSON><PERSON> không hợp lệ'),
  postalCode: z.string().optional(),
  isDefault: z.boolean().default(false),
});

// Lấy danh sách địa chỉ giao hàng của người dùng
export async function GET(request: Request) {
  try {
    await dbConnect();

    // Xác thực người dùng
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    // Lấy danh sách địa chỉ giao hàng
    const addresses = await ShippingAddress.find({ user: user._id }).sort({ isDefault: -1, createdAt: -1 });

    return NextResponse.json(addresses);
  } catch (error) {
    console.error('Error fetching shipping addresses:', error);
    return NextResponse.json(
      { error: 'Lỗi máy chủ nội bộ' },
      { status: 500 }
    );
  }
}

// Thêm địa chỉ giao hàng mới
export async function POST(request: Request) {
  try {
    await dbConnect();

    // Xác thực người dùng
    const user = await getAuthUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    // Xác thực dữ liệu đầu vào
    const body = await request.json();
    const validatedData = shippingAddressSchema.parse(body);

    // Kiểm tra nếu đây là địa chỉ đầu tiên, đặt làm mặc định
    const addressCount = await ShippingAddress.countDocuments({ user: user._id });
    if (addressCount === 0) {
      validatedData.isDefault = true;
    }

    // Tạo địa chỉ giao hàng mới
    const newAddress = await ShippingAddress.create({
      ...validatedData,
      user: user._id,
    });

    return NextResponse.json(newAddress, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error('Error creating shipping address:', error);
    return NextResponse.json(
      { error: 'Lỗi máy chủ nội bộ' },
      { status: 500 }
    );
  }
}
