import { NextRequest, NextResponse } from "next/server"
import dbConnect from "@/lib/mongodb"
import { getAuthUser } from "@/lib/auth-utils"
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Shop from "@/models/Shop"

// Ensure models are registered
ensureModelsRegistered()

export async function GET(request: NextRequest) {
  try {
    await dbConnect()

    // Lấy thông tin người dùng từ token
    const user = await getAuthUser(request)

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      )
    }

    // Chỉ seller và admin mới có thể xem sản phẩm có tồn kho thấp
    if (user.role !== "seller" && user.role !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền xem sản phẩm có tồn kho thấp" },
        { status: 403 }
      )
    }

    // Lấy shop của seller
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop && user.role === "seller") {
      return NextResponse.json(
        { error: "Không tìm thấy shop" },
        { status: 404 }
      )
    }

    // Lấy query params
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get("limit") || "10")
    const threshold = parseInt(searchParams.get("threshold") || "10")

    // Tạo query filter
    const filter: any = {}

    // Nếu là seller, chỉ lấy sản phẩm của shop
    if (user.role === "seller" && shop) {
      filter.shop = shop._id
    }

    // Lấy danh sách sản phẩm có tồn kho thấp
    const products = await Product.find({
      ...filter,
      $expr: {
        $lte: [
          "$stock",
          { $ifNull: ["$minStockThreshold", threshold] }
        ]
      }
    })
      .sort({ stock: 1 })
      .limit(limit)
      .populate("category", "name")

    // Định dạng kết quả
    const formattedProducts = products.map(product => ({
      _id: product._id,
      title: product.title,
      stock: product.stock,
      minStockThreshold: product.minStockThreshold || threshold,
      sku: product.sku || "",
      image: product.images && product.images.length > 0 ? product.images[0] : undefined,
      category: product.category ? product.category.name : "Không phân loại",
      price: product.price
    }))

    return NextResponse.json({
      products: formattedProducts,
      total: formattedProducts.length
    })
  } catch (error) {
    console.error("Error fetching low stock products:", error)
    return NextResponse.json(
      { error: "Lỗi lấy danh sách sản phẩm có tồn kho thấp" },
      { status: 500 }
    )
  }
}
