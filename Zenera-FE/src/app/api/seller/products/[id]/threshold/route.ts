import { NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import { z } from "zod"
import dbConnect from "@/lib/mongodb"
// import { getAuthUser } from "@/lib/auth-utils"
import { getAuthUser } from "@/lib/auth-utils-temp" // Tạm thời bỏ qua xác thực
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Shop from "@/models/Shop"
import InventoryHistory from "@/models/InventoryHistory"

// Ensure models are registered
ensureModelsRegistered()

// Schema validation cho cập nhật ngưỡng tồn kho
const thresholdSchema = z.object({
  minStockThreshold: z.number().int().min(0, "Ngưỡng tồn kho tối thiểu không được âm")
})

// GET: Lấy ngưỡng tồn kho tối thiểu hiện tại
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Get the seller's shop
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop) {
      return NextResponse.json(
        { error: "Shop not found" },
        { status: 404 }
      )
    }

    // Find the product
    const product = await Product.findOne({
      _id: params.id,
      shop: shop._id
    })

    if (!product) {
      return NextResponse.json(
        { error: "Product not found or you don't have permission to access it" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      productId: product._id,
      minStockThreshold: product.minStockThreshold || 0,
      currentStock: product.stock
    })
  } catch (error) {
    console.error("Error fetching stock threshold:", error)
    return NextResponse.json(
      { error: "Failed to fetch stock threshold" },
      { status: 500 }
    )
  }
}

// PUT: Cập nhật ngưỡng tồn kho tối thiểu
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Get the seller's shop
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop) {
      return NextResponse.json(
        { error: "Shop not found" },
        { status: 404 }
      )
    }

    // Parse and validate request body
    const data = await req.json()

    try {
      thresholdSchema.parse(data)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: error.errors },
          { status: 400 }
        )
      }
      throw error
    }

    // Find the product
    const product = await Product.findOne({
      _id: params.id,
      shop: shop._id
    })

    if (!product) {
      return NextResponse.json(
        { error: "Product not found or you don't have permission to update it" },
        { status: 404 }
      )
    }

    // Get previous threshold
    const previousThreshold = product.minStockThreshold || 0

    // Update threshold
    product.minStockThreshold = data.minStockThreshold
    await product.save()

    // Create history entry for threshold update
    const historyEntry = new InventoryHistory({
      product: product._id,
      shop: shop._id,
      user: user._id,
      actionType: "threshold_update",
      quantity: 0, // No quantity change
      previousQuantity: product.stock,
      newQuantity: product.stock,
      notes: `Updated minimum stock threshold from ${previousThreshold} to ${data.minStockThreshold}`
    })

    await historyEntry.save()

    return NextResponse.json({
      success: true,
      productId: product._id,
      minStockThreshold: product.minStockThreshold,
      currentStock: product.stock
    })
  } catch (error) {
    console.error("Error updating stock threshold:", error)
    return NextResponse.json(
      { error: "Failed to update stock threshold" },
      { status: 500 }
    )
  }
}
