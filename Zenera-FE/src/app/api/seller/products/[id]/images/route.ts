import { NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import dbConnect from "@/lib/mongodb"
// import { getAuthUser } from "@/lib/auth-utils"
import { getAuthUser } from "@/lib/auth-utils-temp" // Tạm thời bỏ qua xác thực
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Shop from "@/models/Shop"

// Ensure models are registered
ensureModelsRegistered()

// GET: L<PERSON><PERSON> danh sách hình ảnh của sản phẩm
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Get the seller's shop
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop) {
      return NextResponse.json(
        { error: "Shop not found" },
        { status: 404 }
      )
    }

    // Find the product
    const product = await Product.findOne({
      _id: params.id,
      shop: shop._id
    })

    if (!product) {
      return NextResponse.json(
        { error: "Product not found or you don't have permission to access it" },
        { status: 404 }
      )
    }

    return NextResponse.json({ images: product.images })
  } catch (error) {
    console.error("Error fetching product images:", error)
    return NextResponse.json(
      { error: "Failed to fetch product images" },
      { status: 500 }
    )
  }
}

// POST: Thêm hình ảnh mới vào sản phẩm
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Get the seller's shop
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop) {
      return NextResponse.json(
        { error: "Shop not found" },
        { status: 404 }
      )
    }

    // Parse request body
    const { imageUrl } = await req.json()

    if (!imageUrl) {
      return NextResponse.json(
        { error: "Image URL is required" },
        { status: 400 }
      )
    }

    // Find and update the product
    const product = await Product.findOneAndUpdate(
      { _id: params.id, shop: shop._id },
      { $push: { images: imageUrl } },
      { new: true }
    )

    if (!product) {
      return NextResponse.json(
        { error: "Product not found or you don't have permission to update it" },
        { status: 404 }
      )
    }

    return NextResponse.json({ images: product.images })
  } catch (error) {
    console.error("Error adding product image:", error)
    return NextResponse.json(
      { error: "Failed to add product image" },
      { status: 500 }
    )
  }
}

// PUT: Cập nhật thứ tự hình ảnh
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Get the seller's shop
    const shop = await Shop.findOne({ owner: user._id })

    if (!shop) {
      return NextResponse.json(
        { error: "Shop not found" },
        { status: 404 }
      )
    }

    // Parse request body
    const { images } = await req.json()

    if (!Array.isArray(images)) {
      return NextResponse.json(
        { error: "Images must be an array" },
        { status: 400 }
      )
    }

    // Find and update the product
    const product = await Product.findOneAndUpdate(
      { _id: params.id, shop: shop._id },
      { $set: { images } },
      { new: true }
    )

    if (!product) {
      return NextResponse.json(
        { error: "Product not found or you don't have permission to update it" },
        { status: 404 }
      )
    }

    return NextResponse.json({ images: product.images })
  } catch (error) {
    console.error("Error updating product images:", error)
    return NextResponse.json(
      { error: "Failed to update product images" },
      { status: 500 }
    )
  }
}
