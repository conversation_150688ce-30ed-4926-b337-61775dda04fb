import { NextRequest, NextResponse } from "next/server"
import dbConnect from "@/lib/mongodb"
import { getAuthUser } from "@/lib/auth-utils"
import { ensureModelsRegistered } from "@/lib/models"
import Notification from "@/models/Notification"

// Ensure models are registered
ensureModelsRegistered()

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Lấy thông tin người dùng từ token
    const user = await getAuthUser(request)

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      )
    }

    // Tìm thông báo theo ID
    const notification = await Notification.findById(params.id)

    if (!notification) {
      return NextResponse.json(
        { error: "Thông báo không tồn tại" },
        { status: 404 }
      )
    }

    // Kiểm tra quyền truy cập
    if (notification.user.toString() !== user._id.toString()) {
      return NextResponse.json(
        { error: "Không có quyền truy cập thông báo này" },
        { status: 403 }
      )
    }

    // Đánh dấu thông báo đã đọc
    notification.read = true
    await notification.save()

    return NextResponse.json(notification)
  } catch (error) {
    console.error("Error marking notification as read:", error)
    return NextResponse.json(
      { error: "Lỗi đánh dấu thông báo đã đọc" },
      { status: 500 }
    )
  }
}
