import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import dbConnect from "@/lib/mongodb"
import { getAuthUser } from "@/lib/auth-utils"
import { ensureModelsRegistered } from "@/lib/models"
import User from "@/models/User"

// Ensure models are registered
ensureModelsRegistered()

// Schema cho request body
const settingsSchema = z.object({
  notifications: z.boolean().default(true),
  orderNotifications: z.boolean().default(true),
  inventoryNotifications: z.boolean().default(true),
  paymentNotifications: z.boolean().default(true),
  systemNotifications: z.boolean().default(true),
})

// Lấy cài đặt thông báo
export async function GET(request: NextRequest) {
  try {
    await dbConnect()

    // Lấy thông tin người dùng từ token
    const user = await getAuthUser(request)

    if (!user) {
      return NextResponse.json(
        { error: "<PERSON>h<PERSON>ng có quyền truy cập" },
        { status: 401 }
      )
    }

    // L<PERSON>y cài đặt thông báo từ user preferences
    const preferences = user.preferences || {}
    
    // Trả về cài đặt thông báo
    return NextResponse.json({
      notifications: preferences.notifications !== undefined ? preferences.notifications : true,
      orderNotifications: preferences.orderNotifications !== undefined ? preferences.orderNotifications : true,
      inventoryNotifications: preferences.inventoryNotifications !== undefined ? preferences.inventoryNotifications : true,
      paymentNotifications: preferences.paymentNotifications !== undefined ? preferences.paymentNotifications : true,
      systemNotifications: preferences.systemNotifications !== undefined ? preferences.systemNotifications : true,
    })
  } catch (error) {
    console.error("Error fetching notification settings:", error)
    return NextResponse.json(
      { error: "Lỗi lấy cài đặt thông báo" },
      { status: 500 }
    )
  }
}

// Cập nhật cài đặt thông báo
export async function PUT(request: NextRequest) {
  try {
    await dbConnect()

    // Lấy thông tin người dùng từ token
    const user = await getAuthUser(request)

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      )
    }

    // Lấy dữ liệu từ request body
    const body = await request.json()

    // Validate dữ liệu
    const validatedData = settingsSchema.parse(body)

    // Cập nhật cài đặt thông báo
    const updatedUser = await User.findByIdAndUpdate(
      user._id,
      {
        $set: {
          "preferences.notifications": validatedData.notifications,
          "preferences.orderNotifications": validatedData.orderNotifications,
          "preferences.inventoryNotifications": validatedData.inventoryNotifications,
          "preferences.paymentNotifications": validatedData.paymentNotifications,
          "preferences.systemNotifications": validatedData.systemNotifications,
        },
      },
      { new: true }
    )

    // Trả về cài đặt thông báo đã cập nhật
    return NextResponse.json({
      notifications: updatedUser.preferences?.notifications,
      orderNotifications: updatedUser.preferences?.orderNotifications,
      inventoryNotifications: updatedUser.preferences?.inventoryNotifications,
      paymentNotifications: updatedUser.preferences?.paymentNotifications,
      systemNotifications: updatedUser.preferences?.systemNotifications,
    })
  } catch (error) {
    console.error("Error updating notification settings:", error)
    return NextResponse.json(
      { error: "Lỗi cập nhật cài đặt thông báo" },
      { status: 500 }
    )
  }
}
