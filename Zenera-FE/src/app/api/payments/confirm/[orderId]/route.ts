import { NextResponse } from "next/server";
import { z } from "zod";
import dbConnect from "@/lib/mongodb";
import { ensureModelsRegistered, Order, Shop } from "@/lib/models";
import { getAuthUser } from "@/lib/auth-utils";

// Schema cho xác nhận thanh toán
const confirmPaymentSchema = z.object({
  transactionId: z.string().optional(),
  note: z.string().optional(),
});

export async function POST(
  request: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    await dbConnect();

    // Đảm bảo tất cả các models được đăng ký
    ensureModelsRegistered();

    const user = await getAuthUser(request);

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Chỉ admin hoặc seller mới có thể xác nhận thanh toán
    if (user.role !== "admin" && user.role !== "seller") {
      return NextResponse.json(
        { error: "Không có quyền xác nhận thanh toán" },
        { status: 403 }
      );
    }

    const orderId = params.orderId;
    const body = await request.json();
    
    // Validate dữ liệu đầu vào
    const validatedData = confirmPaymentSchema.parse(body);

    // Tìm đơn hàng
    const order = await Order.findById(orderId)
      .populate({
        path: "items.product",
        select: "shop",
      });

    if (!order) {
      return NextResponse.json(
        { error: "Đơn hàng không tồn tại" },
        { status: 404 }
      );
    }

    // Kiểm tra quyền xác nhận thanh toán
    // - Admin có thể xác nhận tất cả đơn hàng
    // - Seller chỉ có thể xác nhận đơn hàng có sản phẩm của shop mình
    if (user.role === "seller") {
      let hasPermission = false;
      
      // Kiểm tra xem seller có sản phẩm trong đơn hàng không
      for (const item of order.items) {
        if (item.product && item.product.shop) {
          const shopId = item.product.shop._id || item.product.shop;
          const shop = await Shop.findOne({ _id: shopId, owner: user._id });
          if (shop) {
            hasPermission = true;
            break;
          }
        }
      }

      if (!hasPermission) {
        return NextResponse.json(
          { error: "Không có quyền xác nhận thanh toán cho đơn hàng này" },
          { status: 403 }
        );
      }
    }

    // Kiểm tra trạng thái thanh toán
    if (order.paymentStatus === "paid") {
      return NextResponse.json(
        { error: "Đơn hàng này đã được thanh toán" },
        { status: 400 }
      );
    }

    // Cập nhật trạng thái thanh toán
    order.paymentStatus = "paid";
    
    // Thêm vào lịch sử trạng thái
    order.statusHistory.push({
      status: order.status,
      timestamp: new Date(),
      note: validatedData.note || "Thanh toán đã được xác nhận",
    });

    // Nếu đơn hàng đang ở trạng thái pending, cập nhật thành processing
    if (order.status === "pending") {
      order.status = "processing";
      order.statusHistory.push({
        status: "processing",
        timestamp: new Date(),
        note: "Đơn hàng đã được thanh toán và đang được xử lý",
      });
    }

    await order.save();

    return NextResponse.json({
      message: "Xác nhận thanh toán thành công",
      order,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error confirming payment:", error);
    return NextResponse.json(
      { error: "Lỗi xác nhận thanh toán" },
      { status: 500 }
    );
  }
}
