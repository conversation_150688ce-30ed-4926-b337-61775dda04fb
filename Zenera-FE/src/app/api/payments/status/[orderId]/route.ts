import { NextResponse } from "next/server";
import dbConnect from "@/lib/mongodb";
import { ensureModelsRegistered, Order } from "@/lib/models";
import { getAuthUser } from "@/lib/auth-utils";

export async function GET(
  request: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    await dbConnect();

    // Đảm bảo tất cả các models được đăng ký
    ensureModelsRegistered();

    const user = await getAuthUser(request);

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const orderId = params.orderId;

    // Tìm đơn hàng
    const order = await Order.findById(orderId);

    if (!order) {
      return NextResponse.json(
        { error: "Đơn hàng không tồn tại" },
        { status: 404 }
      );
    }

    // Kiểm tra quyền truy cập
    // - Ng<PERSON>ời dùng chỉ có thể xem trạng thái thanh toán của đơn hàng của mình
    // - Admin có thể xem tất cả
    if (order.user.toString() !== user._id.toString() && user.role !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền xem trạng thái thanh toán của đơn hàng này" },
        { status: 403 }
      );
    }

    // Trả về thông tin thanh toán
    return NextResponse.json({
      orderId: order._id,
      orderNumber: order.orderNumber,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      totalAmount: order.totalAmount,
      finalAmount: order.finalAmount,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    });
  } catch (error) {
    console.error("Error checking payment status:", error);
    return NextResponse.json(
      { error: "Lỗi kiểm tra trạng thái thanh toán" },
      { status: 500 }
    );
  }
}
