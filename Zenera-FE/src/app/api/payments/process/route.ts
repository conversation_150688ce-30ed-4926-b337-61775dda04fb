import { NextResponse } from "next/server";
import { z } from "zod";
import dbConnect from "@/lib/mongodb";
import { ensureModelsRegistered, Order } from "@/lib/models";
import { getAuthUser } from "@/lib/auth-utils";

// Schema cho xử lý thanh toán
const processPaymentSchema = z.object({
  orderId: z.string().min(1, "ID đơn hàng không được để trống"),
  paymentMethod: z.enum(["credit_card", "bank_transfer", "cash", "cod"]),
  paymentDetails: z.object({
    cardNumber: z.string().optional(),
    cardHolder: z.string().optional(),
    expiryDate: z.string().optional(),
    cvv: z.string().optional(),
    bankAccount: z.string().optional(),
    bankName: z.string().optional(),
    transactionId: z.string().optional(),
  }).optional(),
});

export async function POST(request: Request) {
  try {
    await dbConnect();

    // Đảm bảo tất cả các models được đăng ký
    ensureModelsRegistered();

    const user = await getAuthUser(request);

    if (!user) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate dữ liệu đầu vào
    const validatedData = processPaymentSchema.parse(body);

    // Tìm đơn hàng
    const order = await Order.findById(validatedData.orderId);

    if (!order) {
      return NextResponse.json(
        { error: "Đơn hàng không tồn tại" },
        { status: 404 }
      );
    }

    // Kiểm tra quyền thanh toán
    // Chỉ người dùng tạo đơn hàng mới có thể thanh toán
    if (order.user.toString() !== user._id.toString()) {
      return NextResponse.json(
        { error: "Không có quyền thanh toán đơn hàng này" },
        { status: 403 }
      );
    }

    // Kiểm tra trạng thái thanh toán
    if (order.paymentStatus === "paid") {
      return NextResponse.json(
        { error: "Đơn hàng này đã được thanh toán" },
        { status: 400 }
      );
    }

    // Xử lý thanh toán dựa trên phương thức thanh toán
    let paymentStatus = "pending";
    let paymentNote = "";

    switch (validatedData.paymentMethod) {
      case "credit_card":
        // Mô phỏng xử lý thanh toán thẻ tín dụng
        // Trong thực tế, bạn sẽ tích hợp với cổng thanh toán
        paymentStatus = "paid";
        paymentNote = "Thanh toán thẻ tín dụng thành công";
        break;
      case "bank_transfer":
        // Đối với chuyển khoản ngân hàng, trạng thái vẫn là pending
        // cho đến khi admin xác nhận
        paymentStatus = "pending";
        paymentNote = "Đang chờ xác nhận chuyển khoản ngân hàng";
        break;
      case "cash":
        // Thanh toán tiền mặt khi nhận hàng
        paymentStatus = "pending";
        paymentNote = "Thanh toán tiền mặt khi nhận hàng";
        break;
      case "cod":
        // Thanh toán khi nhận hàng
        paymentStatus = "pending";
        paymentNote = "Thanh toán khi nhận hàng";
        break;
    }

    // Cập nhật thông tin thanh toán
    order.paymentMethod = validatedData.paymentMethod;
    order.paymentStatus = paymentStatus;
    
    // Thêm vào lịch sử trạng thái
    order.statusHistory.push({
      status: order.status,
      timestamp: new Date(),
      note: paymentNote,
    });

    // Nếu thanh toán thành công và đơn hàng đang ở trạng thái pending
    // thì cập nhật trạng thái đơn hàng thành processing
    if (paymentStatus === "paid" && order.status === "pending") {
      order.status = "processing";
      order.statusHistory.push({
        status: "processing",
        timestamp: new Date(),
        note: "Đơn hàng đã được thanh toán và đang được xử lý",
      });
    }

    await order.save();

    return NextResponse.json({
      message: "Xử lý thanh toán thành công",
      order,
      paymentStatus,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error processing payment:", error);
    return NextResponse.json(
      { error: "Lỗi xử lý thanh toán" },
      { status: 500 }
    );
  }
}
