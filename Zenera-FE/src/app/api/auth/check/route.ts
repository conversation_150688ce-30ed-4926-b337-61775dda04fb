import { NextRequest, NextResponse } from "next/server"
import { getAuthUser } from "@/lib/auth-utils"
import { verifyToken } from "@/lib/jwt"
import { cookies } from "next/headers"

export async function GET(req: NextRequest) {
  try {
    console.log('Auth check API: Attempting to get auth user')
    const user = await getAuthUser(req)

    if (!user) {
      console.log('Auth check API: Authentication failed - no user found')

      // Kiểm tra xem token có hết hạn không
      const cookieStore = cookies();
      const token = cookieStore.get('token')?.value;

      if (token) {
        const decoded = verifyToken(token);
        if (!decoded) {
          // Token hết hạn hoặc không hợp lệ
          return NextResponse.json(
            {
              authenticated: false,
              message: "Token expired",
              tokenExpired: true
            },
            { status: 401 }
          )
        }
      }

      return NextResponse.json(
        { authenticated: false, message: "Not authenticated" },
        { status: 401 }
      )
    }

    console.log('Auth check API: User authenticated successfully:', user._id)
    return NextResponse.json({
      authenticated: true,
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        avatar: user.avatar,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    })
  } catch (error) {
    console.error('Auth check API error:', error)
    return NextResponse.json(
      { authenticated: false, message: "Error checking authentication" },
      { status: 500 }
    )
  }
}
