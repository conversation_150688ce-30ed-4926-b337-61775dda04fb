import { NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import { z } from "zod"
import dbConnect from "@/lib/mongodb"
import { getAuthUser } from "@/lib/auth-utils"
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Review from "@/models/Review"

// Ensure models are registered
ensureModelsRegistered()

// Schema validation cho đánh giá mới
const reviewSchema = z.object({
  rating: z.number().min(1).max(5),
  comment: z.string().min(3, "Nội dung đánh giá phải có ít nhất 3 ký tự")
})

// GET: L<PERSON>y danh sách đánh giá của sản phẩm
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(req.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const sort = searchParams.get('sort') || 'newest'

    // Check if product exists
    const product = await Product.findById(params.id)

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    // Build sort options
    let sortOptions = {}
    switch (sort) {
      case 'newest':
        sortOptions = { createdAt: -1 }
        break
      case 'oldest':
        sortOptions = { createdAt: 1 }
        break
      case 'highest':
        sortOptions = { rating: -1 }
        break
      case 'lowest':
        sortOptions = { rating: 1 }
        break
      default:
        sortOptions = { createdAt: -1 }
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Get total count
    const total = await Review.countDocuments({ product: params.id })

    // Get reviews
    const reviews = await Review.find({ product: params.id })
      .populate('user', 'name avatar')
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)

    return NextResponse.json({
      reviews,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    })
  } catch (error) {
    console.error("Error fetching product reviews:", error)
    return NextResponse.json(
      { error: "Failed to fetch product reviews" },
      { status: 500 }
    )
  }
}

// POST: Thêm đánh giá mới
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate product ID
    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: "Invalid product ID" },
        { status: 400 }
      )
    }

    // Check if product exists
    const product = await Product.findById(params.id)

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    // Check if user has already reviewed this product
    const existingReview = await Review.findOne({
      product: params.id,
      user: user._id
    })

    if (existingReview) {
      return NextResponse.json(
        { error: "You have already reviewed this product" },
        { status: 400 }
      )
    }

    // Parse and validate request body
    const data = await req.json()
    
    try {
      reviewSchema.parse(data)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: error.errors },
          { status: 400 }
        )
      }
      throw error
    }

    // Create new review
    const review = new Review({
      product: params.id,
      user: user._id,
      rating: data.rating,
      comment: data.comment
    })

    await review.save()

    // Update product rating and review count
    const allReviews = await Review.find({ product: params.id })
    const totalRating = allReviews.reduce((sum, review) => sum + review.rating, 0)
    const averageRating = totalRating / allReviews.length

    product.rating = averageRating
    product.reviews = allReviews.length
    await product.save()

    // Populate user information
    await review.populate('user', 'name avatar')

    return NextResponse.json(review, { status: 201 })
  } catch (error) {
    console.error("Error creating product review:", error)
    return NextResponse.json(
      { error: "Failed to create product review" },
      { status: 500 }
    )
  }
}
