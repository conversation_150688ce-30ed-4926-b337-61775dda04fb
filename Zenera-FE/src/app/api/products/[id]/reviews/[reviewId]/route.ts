import { NextRequest, NextResponse } from "next/server"
import mongoose from "mongoose"
import { z } from "zod"
import dbConnect from "@/lib/mongodb"
import { getAuthUser } from "@/lib/auth-utils"
import { ensureModelsRegistered } from "@/lib/models"
import Product from "@/models/Product"
import Review from "@/models/Review"

// Ensure models are registered
ensureModelsRegistered()

// Schema validation cho cập nhật đ<PERSON>h giá
const updateReviewSchema = z.object({
  rating: z.number().min(1).max(5).optional(),
  comment: z.string().min(3, "Nội dung đánh giá phải có ít nhất 3 ký tự").optional()
})

// GET: Lấy chi tiết một đánh giá
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string; reviewId: string } }
) {
  try {
    await dbConnect()

    // Validate IDs
    if (!mongoose.Types.ObjectId.isValid(params.id) || !mongoose.Types.ObjectId.isValid(params.reviewId)) {
      return NextResponse.json(
        { error: "Invalid ID format" },
        { status: 400 }
      )
    }

    // Find the review
    const review = await Review.findOne({
      _id: params.reviewId,
      product: params.id
    }).populate('user', 'name avatar')

    if (!review) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      )
    }

    return NextResponse.json(review)
  } catch (error) {
    console.error("Error fetching review:", error)
    return NextResponse.json(
      { error: "Failed to fetch review" },
      { status: 500 }
    )
  }
}

// PUT: Cập nhật đánh giá
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string; reviewId: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate IDs
    if (!mongoose.Types.ObjectId.isValid(params.id) || !mongoose.Types.ObjectId.isValid(params.reviewId)) {
      return NextResponse.json(
        { error: "Invalid ID format" },
        { status: 400 }
      )
    }

    // Find the review
    const review = await Review.findOne({
      _id: params.reviewId,
      product: params.id
    })

    if (!review) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      )
    }

    // Check if the user is the owner of the review
    if (review.user.toString() !== user._id.toString()) {
      return NextResponse.json(
        { error: "You can only update your own reviews" },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const data = await req.json()
    
    try {
      updateReviewSchema.parse(data)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation error", details: error.errors },
          { status: 400 }
        )
      }
      throw error
    }

    // Update review
    if (data.rating !== undefined) {
      review.rating = data.rating
    }
    
    if (data.comment !== undefined) {
      review.comment = data.comment
    }

    await review.save()

    // Update product rating
    const product = await Product.findById(params.id)
    if (product) {
      const allReviews = await Review.find({ product: params.id })
      const totalRating = allReviews.reduce((sum, review) => sum + review.rating, 0)
      const averageRating = totalRating / allReviews.length

      product.rating = averageRating
      await product.save()
    }

    // Populate user information
    await review.populate('user', 'name avatar')

    return NextResponse.json(review)
  } catch (error) {
    console.error("Error updating review:", error)
    return NextResponse.json(
      { error: "Failed to update review" },
      { status: 500 }
    )
  }
}

// DELETE: Xóa đánh giá
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string; reviewId: string } }
) {
  try {
    await dbConnect()

    // Get the current user from auth token
    const user = await getAuthUser(req)

    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Validate IDs
    if (!mongoose.Types.ObjectId.isValid(params.id) || !mongoose.Types.ObjectId.isValid(params.reviewId)) {
      return NextResponse.json(
        { error: "Invalid ID format" },
        { status: 400 }
      )
    }

    // Find the review
    const review = await Review.findOne({
      _id: params.reviewId,
      product: params.id
    })

    if (!review) {
      return NextResponse.json(
        { error: "Review not found" },
        { status: 404 }
      )
    }

    // Check if the user is the owner of the review or an admin
    if (review.user.toString() !== user._id.toString() && user.role !== 'admin') {
      return NextResponse.json(
        { error: "You can only delete your own reviews" },
        { status: 403 }
      )
    }

    // Delete the review
    await Review.deleteOne({ _id: params.reviewId })

    // Update product rating and review count
    const product = await Product.findById(params.id)
    if (product) {
      const allReviews = await Review.find({ product: params.id })
      
      if (allReviews.length === 0) {
        product.rating = 0
        product.reviews = 0
      } else {
        const totalRating = allReviews.reduce((sum, review) => sum + review.rating, 0)
        const averageRating = totalRating / allReviews.length

        product.rating = averageRating
        product.reviews = allReviews.length
      }
      
      await product.save()
    }

    return NextResponse.json({
      success: true,
      message: "Review deleted successfully"
    })
  } catch (error) {
    console.error("Error deleting review:", error)
    return NextResponse.json(
      { error: "Failed to delete review" },
      { status: 500 }
    )
  }
}
