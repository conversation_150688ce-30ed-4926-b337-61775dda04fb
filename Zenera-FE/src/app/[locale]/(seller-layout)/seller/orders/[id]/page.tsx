"use client"

import { useState } from "react"
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { useTranslation } from "react-i18next"
import { format } from "date-fns"
import {
  Package,
  Truck,
  CheckCircle,
  XCircle,
  Clock,
  ArrowLeft,
  Send,
  AlertCircle,
  Printer
} from "lucide-react"
import { PrintInvoice } from "@components/orders/print-invoice"

import { Button } from "@components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@components/ui/card"
import { Separator } from "@components/ui/separator"
import { Badge } from "@components/ui/badge"
import { Skeleton } from "@components/ui/skeleton"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rig<PERSON>,
} from "@components/ui/dialog"
import { Textarea } from "@components/ui/textarea"
import { toast } from "sonner"
import { OrderTimeline } from "@components/orders/order-timeline"

// Tạo service cho seller order
const sellerOrderService = {
  getOrderDetails: async (orderId: string) => {
    const response = await fetch(`/api/orders/${orderId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch order details");
    }

    return response.json();
  },

  updateOrderStatus: async (orderId: string, data: { status: string; note?: string }) => {
    const response = await fetch(`/api/orders/${orderId}/status`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update order status");
    }

    return response.json();
  },

  sendConfirmationEmail: async (orderId: string) => {
    const response = await fetch(`/api/orders/${orderId}/send-confirmation`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error("Failed to send confirmation email");
    }

    return response.json();
  },
};

// Status badge component
const OrderStatusBadge = ({ status }: { status: string }) => {
  let variant: "default" | "secondary" | "destructive" | "outline" = "default";
  let icon = null;

  switch (status) {
    case "pending":
      variant = "outline";
      icon = <Clock className="h-4 w-4 mr-1" />;
      break;
    case "processing":
      variant = "secondary";
      icon = <Package className="h-4 w-4 mr-1" />;
      break;
    case "shipped":
      variant = "default";
      icon = <Truck className="h-4 w-4 mr-1" />;
      break;
    case "delivered":
      variant = "default";
      icon = <CheckCircle className="h-4 w-4 mr-1" />;
      break;
    case "cancelled":
      variant = "destructive";
      icon = <XCircle className="h-4 w-4 mr-1" />;
      break;
  }

  return (
    <Badge variant={variant} className="flex items-center">
      {icon}
      <span className="capitalize">{status}</span>
    </Badge>
  );
};

export default function SellerOrderDetailsPage() {
  const { t } = useTranslation("orders");
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const orderId = params.id as string;

  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState("");
  const [statusNote, setStatusNote] = useState("");

  // Fetch order details
  const { data: order, isLoading, error } = useQuery({
    queryKey: ["sellerOrder", orderId],
    queryFn: () => sellerOrderService.getOrderDetails(orderId),
  });

  // Update order status mutation
  const updateStatusMutation = useMutation({
    mutationFn: (data: { status: string; note?: string }) =>
      sellerOrderService.updateOrderStatus(orderId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["sellerOrder", orderId] });
      toast.success(t("statusUpdateSuccess") || "Cập nhật trạng thái thành công");
      setIsStatusDialogOpen(false);
    },
    onError: (error) => {
      toast.error(
        error instanceof Error
          ? error.message
          : t("statusUpdateError") || "Lỗi cập nhật trạng thái"
      );
    },
  });

  // Send confirmation email mutation
  const sendEmailMutation = useMutation({
    mutationFn: () => sellerOrderService.sendConfirmationEmail(orderId),
    onSuccess: () => {
      toast.success(t("emailSentSuccess") || "Đã gửi email xác nhận");
    },
    onError: (error) => {
      toast.error(
        error instanceof Error
          ? error.message
          : t("emailSentError") || "Lỗi gửi email xác nhận"
      );
    },
  });

  // Handle status update
  const handleStatusUpdate = () => {
    if (!newStatus) {
      toast.error(t("selectStatusError") || "Vui lòng chọn trạng thái");
      return;
    }

    updateStatusMutation.mutate({
      status: newStatus,
      note: statusNote,
    });
  };

  // Removed handlePrintInvoice as we're using the PrintInvoice component

  // Handle send confirmation email
  const handleSendEmail = () => {
    sendEmailMutation.mutate();
  };

  // Handle back button
  const handleBack = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
          <div>
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Button variant="outline" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">{t("orderNotFound") || "Không tìm thấy đơn hàng"}</h1>
        </div>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              {t("error") || "Lỗi"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error instanceof Error ? error.message : String(error) || t("orderNotFoundDesc") || "Không thể tải thông tin đơn hàng. Vui lòng thử lại sau."}</p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={handleBack}>
              {t("backToOrders") || "Quay lại danh sách đơn hàng"}
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">
            {t("orderDetails") || "Chi tiết đơn hàng"} #{order.orderNumber || order._id}
          </h1>
        </div>
        <div className="flex flex-wrap gap-2">
          <PrintInvoice order={order} />
          <Button variant="outline" size="sm" onClick={handleSendEmail} disabled={sendEmailMutation.isPending}>
            <Send className="h-4 w-4 mr-2" />
            {t("sendConfirmation") || "Gửi email xác nhận"}
          </Button>
          <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                {t("updateStatus") || "Cập nhật trạng thái"}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{t("updateOrderStatus") || "Cập nhật trạng thái đơn hàng"}</DialogTitle>
                <DialogDescription>
                  {t("updateOrderStatusDesc") || "Chọn trạng thái mới cho đơn hàng này."}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {t("currentStatus") || "Trạng thái hiện tại"}:
                  </label>
                  <div>
                    <OrderStatusBadge status={order.status} />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {t("newStatus") || "Trạng thái mới"}:
                  </label>
                  <Select onValueChange={setNewStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectStatus") || "Chọn trạng thái"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">{t("pending") || "Chờ xử lý"}</SelectItem>
                      <SelectItem value="processing">{t("processing") || "Đang xử lý"}</SelectItem>
                      <SelectItem value="shipped">{t("shipped") || "Đang giao hàng"}</SelectItem>
                      <SelectItem value="delivered">{t("delivered") || "Đã giao hàng"}</SelectItem>
                      <SelectItem value="cancelled">{t("cancelled") || "Đã hủy"}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {t("note") || "Ghi chú"} ({t("optional") || "tùy chọn"}):
                  </label>
                  <Textarea
                    placeholder={t("noteForStatusChange") || "Ghi chú cho việc thay đổi trạng thái"}
                    value={statusNote}
                    onChange={(e) => setStatusNote(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsStatusDialogOpen(false)}>
                  {t("cancel") || "Hủy"}
                </Button>
                <Button onClick={handleStatusUpdate} disabled={updateStatusMutation.isPending}>
                  {updateStatusMutation.isPending
                    ? t("updating") || "Đang cập nhật..."
                    : t("updateStatus") || "Cập nhật trạng thái"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t("orderInformation") || "Thông tin đơn hàng"}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">{t("orderNumber") || "Mã đơn hàng"}</p>
                  <p className="font-medium">{order.orderNumber || order._id}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t("orderDate") || "Ngày đặt hàng"}</p>
                  <p className="font-medium">
                    {order.createdAt ? format(new Date(order.createdAt), "dd/MM/yyyy HH:mm") : "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t("status") || "Trạng thái"}</p>
                  <OrderStatusBadge status={order.status} />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t("paymentStatus") || "Trạng thái thanh toán"}</p>
                  <Badge variant={order.paymentStatus === "paid" ? "default" : "outline"}>
                    {order.paymentStatus === "paid" ? t("paid") || "Đã thanh toán" : t("pending") || "Chờ thanh toán"}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t("paymentMethod") || "Phương thức thanh toán"}</p>
                  <p className="font-medium">
                    {order.paymentMethod === "credit_card"
                      ? t("creditCard") || "Thẻ tín dụng"
                      : order.paymentMethod === "bank_transfer"
                      ? t("bankTransfer") || "Chuyển khoản ngân hàng"
                      : t("cashOnDelivery") || "Thanh toán khi nhận hàng"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t("shippingMethod") || "Phương thức vận chuyển"}</p>
                  <p className="font-medium">
                    {order.shippingMethod === "standard"
                      ? t("standardShipping") || "Vận chuyển tiêu chuẩn"
                      : order.shippingMethod === "express"
                      ? t("expressShipping") || "Vận chuyển nhanh"
                      : t("pickup") || "Nhận tại cửa hàng"}
                  </p>
                </div>
              </div>

              <Separator className="my-6" />

              <div>
                <h3 className="font-semibold mb-4">{t("customer") || "Khách hàng"}</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">{t("name") || "Tên"}</p>
                    <p className="font-medium">{order.user?.name || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t("email") || "Email"}</p>
                    <p className="font-medium">{order.user?.email || "N/A"}</p>
                  </div>
                </div>
              </div>

              <Separator className="my-6" />

              <div>
                <h3 className="font-semibold mb-4">{t("shippingAddress") || "Địa chỉ giao hàng"}</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">{t("name") || "Tên"}</p>
                    <p className="font-medium">{order.shippingAddress?.fullName || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">{t("phone") || "Điện thoại"}</p>
                    <p className="font-medium">{order.shippingAddress?.phone || "N/A"}</p>
                  </div>
                  <div className="col-span-2">
                    <p className="text-sm text-muted-foreground">{t("address") || "Địa chỉ"}</p>
                    <p className="font-medium">
                      {order.shippingAddress
                        ? `${order.shippingAddress.address}, ${order.shippingAddress.city}, ${order.shippingAddress.state}, ${order.shippingAddress.country}, ${order.shippingAddress.zipCode}`
                        : "N/A"}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t("orderItems") || "Sản phẩm"}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items?.map((item: any) => (
                  <div key={item._id} className="flex items-start gap-4 pb-4 border-b">
                    <div className="w-16 h-16 bg-muted rounded relative overflow-hidden">
                      {item.product?.images?.[0] && (
                        <img
                          src={item.product.images[0]}
                          alt={item.product.title || "Product"}
                          className="object-cover w-full h-full"
                        />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{item.product?.title || "Unknown Product"}</h4>
                      <p className="text-sm text-muted-foreground">
                        {t("quantity") || "Số lượng"}: {item.quantity}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {new Intl.NumberFormat("vi-VN", {
                          style: "currency",
                          currency: "VND",
                        }).format(item.price)}
                      </p>
                      <p className="text-sm font-medium">
                        {new Intl.NumberFormat("vi-VN", {
                          style: "currency",
                          currency: "VND",
                        }).format(item.price * item.quantity)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t("subtotal") || "Tạm tính"}</span>
                  <span>
                    {new Intl.NumberFormat("vi-VN", {
                      style: "currency",
                      currency: "VND",
                    }).format(order.totalAmount || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t("shipping") || "Phí vận chuyển"}</span>
                  <span>
                    {new Intl.NumberFormat("vi-VN", {
                      style: "currency",
                      currency: "VND",
                    }).format(order.shippingFee || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t("tax") || "Thuế"}</span>
                  <span>
                    {new Intl.NumberFormat("vi-VN", {
                      style: "currency",
                      currency: "VND",
                    }).format(order.tax || 0)}
                  </span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-medium">
                  <span>{t("total") || "Tổng cộng"}</span>
                  <span>
                    {new Intl.NumberFormat("vi-VN", {
                      style: "currency",
                      currency: "VND",
                    }).format(order.finalAmount || 0)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>{t("orderTimeline") || "Lịch sử đơn hàng"}</CardTitle>
            </CardHeader>
            <CardContent>
              <OrderTimeline statusHistory={order.statusHistory || []} />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
