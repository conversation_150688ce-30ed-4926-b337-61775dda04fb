"use client"

import { useTranslation } from "react-i18next"
import { useRouter } from "next/navigation"
import { ArrowLeft } from "lucide-react"
import { Button } from "@components/ui/button"
import Link from "next/link"
import ProductForm from "@components/seller/product-form/index"

export default function AddProductPage() {
  const { t } = useTranslation("seller")
  const router = useRouter()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/seller/products/list">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">{t("back") || "Back"}</span>
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">
            {t("addProduct") || "Add Product"}
          </h2>
        </div>
      </div>

      <ProductForm 
        onSuccess={() => {
          router.push("/seller/products/list")
        }}
      />
    </div>
  )
}
