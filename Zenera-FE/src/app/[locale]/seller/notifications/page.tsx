"use client"

import { useState } from "react"
import { useNotifications } from "@hooks/use-notifications"
import { useNotificationsState } from "@store/notifications/notifications.state"
import { formatDistanceToNow } from "date-fns"
import { vi } from "date-fns/locale"
import { useRouter } from "next/navigation"
import { useTranslation } from "react-i18next"
import { Button } from "@components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@components/ui/tabs"
import { Pagination } from "@components/ui/pagination"
import { Badge } from "@components/ui/badge"
import { Bell, Package, ShoppingCart, CreditCard, Info } from "lucide-react"
import NotificationSettings from "@components/notifications/notification-settings"

export default function NotificationsPage() {
  const { t } = useTranslation("notifications")
  const router = useRouter()
  const [page, setPage] = useState(1)
  const [limit] = useState(10)
  const [activeTab, setActiveTab] = useState<"all" | "order" | "inventory" | "payment" | "system">("all")
  const { isLoading, markAsRead, markAllAsRead } = useNotifications(page, limit, "all", activeTab)
  const notificationsState = useNotificationsState()

  // Xử lý khi thay đổi tab
  const handleTabChange = (value: string) => {
    setActiveTab(value as "all" | "order" | "inventory" | "payment" | "system")
    setPage(1)
  }

  // Xử lý khi click vào thông báo
  const handleNotificationClick = (id: string, type: string, data?: Record<string, any>) => {
    markAsRead(id)
    
    // Chuyển hướng dựa vào loại thông báo
    switch (type) {
      case "order":
        if (data?.orderId) {
          router.push(`/seller/orders/${data.orderId}`)
        } else {
          router.push("/seller/orders")
        }
        break
      case "inventory":
        router.push("/seller/inventory")
        break
      case "payment":
        if (data?.orderId) {
          router.push(`/seller/orders/${data.orderId}`)
        } else {
          router.push("/seller/orders")
        }
        break
      default:
        // Không chuyển hướng
    }
  }

  // Lấy icon cho từng loại thông báo
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "order":
        return <ShoppingCart className="h-5 w-5" />
      case "inventory":
        return <Package className="h-5 w-5" />
      case "payment":
        return <CreditCard className="h-5 w-5" />
      default:
        return <Info className="h-5 w-5" />
    }
  }

  // Lấy màu cho từng loại thông báo
  const getNotificationColor = (type: string) => {
    switch (type) {
      case "order":
        return "bg-blue-100 text-blue-700"
      case "inventory":
        return "bg-amber-100 text-amber-700"
      case "payment":
        return "bg-green-100 text-green-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  return (
    <div className="container py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{t("notifications")}</h1>
        <Button onClick={() => markAllAsRead()} disabled={notificationsState.unreadCount.get() === 0}>
          {t("markAllAsRead")}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>{t("yourNotifications")}</CardTitle>
              <CardDescription>
                {t("unreadNotifications", { count: notificationsState.unreadCount.get() })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all" onValueChange={handleTabChange}>
                <TabsList className="mb-4">
                  <TabsTrigger value="all">{t("all")}</TabsTrigger>
                  <TabsTrigger value="order">{t("orders")}</TabsTrigger>
                  <TabsTrigger value="inventory">{t("inventory")}</TabsTrigger>
                  <TabsTrigger value="payment">{t("payments")}</TabsTrigger>
                  <TabsTrigger value="system">{t("system")}</TabsTrigger>
                </TabsList>

                <TabsContent value={activeTab} className="mt-0">
                  {isLoading ? (
                    <div className="p-4 text-center text-muted-foreground">{t("loading")}</div>
                  ) : notificationsState.notifications.get().length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">{t("noNotifications")}</div>
                  ) : (
                    <div className="space-y-4">
                      {notificationsState.notifications.get().map((notification) => (
                        <div
                          key={notification._id}
                          className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-muted ${
                            !notification.read ? "border-l-4 border-l-primary" : ""
                          }`}
                          onClick={() => handleNotificationClick(notification._id, notification.type, notification.data)}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`p-2 rounded-full ${getNotificationColor(notification.type)}`}>
                              {getNotificationIcon(notification.type)}
                            </div>
                            <div className="flex-1">
                              <div className="flex justify-between items-start">
                                <h3 className="font-medium">{notification.title}</h3>
                                <div className="text-xs text-muted-foreground">
                                  {formatDistanceToNow(new Date(notification.createdAt), {
                                    addSuffix: true,
                                    locale: vi,
                                  })}
                                </div>
                              </div>
                              <p className="text-muted-foreground mt-1">{notification.message}</p>
                              {!notification.read && (
                                <Badge variant="outline" className="mt-2">
                                  {t("new")}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}

                      {notificationsState.pagination.get().totalPages > 1 && (
                        <Pagination
                          currentPage={page}
                          totalPages={notificationsState.pagination.get().totalPages}
                          onPageChange={setPage}
                        />
                      )}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div>
          <NotificationSettings />
        </div>
      </div>
    </div>
  )
}
