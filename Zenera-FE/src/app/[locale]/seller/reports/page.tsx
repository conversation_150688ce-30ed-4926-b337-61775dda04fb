"use client"

import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@components/ui/tabs"
import { Button } from "@components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { DatePicker } from "@components/ui/date-picker"
import { useAnalytics } from "@hooks/use-analytics"
import { FileDown, FileText, BarChart, Package, DollarSign } from "lucide-react"
import { format } from "date-fns"
import { vi } from "date-fns/locale"
import { toast } from "sonner"

export default function ReportsPage() {
  const { t } = useTranslation("reports")
  const [reportType, setReportType] = useState<"revenue" | "products" | "inventory">("revenue")
  const [timeRange, setTimeRange] = useState<"day" | "week" | "month" | "year" | "custom">("month")
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)
  const [exportFormat, setExportFormat] = useState<"csv" | "json">("csv")
  const [filters, setFilters] = useState<Record<string, any>>({})
  
  const { generateReport, isGeneratingReport, generatedReport, exportReport, isExportingReport } = useAnalytics()
  
  // Xử lý khi thay đổi loại báo cáo
  const handleReportTypeChange = (value: string) => {
    setReportType(value as "revenue" | "products" | "inventory")
    // Reset filters khi thay đổi loại báo cáo
    setFilters({})
  }
  
  // Xử lý khi thay đổi khoảng thời gian
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value as "day" | "week" | "month" | "year" | "custom")
    
    // Reset ngày bắt đầu và kết thúc nếu không phải custom
    if (value !== "custom") {
      setStartDate(undefined)
      setEndDate(undefined)
    } else {
      // Nếu là custom và chưa có ngày, thiết lập mặc định
      if (!startDate) {
        const today = new Date()
        const lastMonth = new Date()
        lastMonth.setMonth(today.getMonth() - 1)
        setStartDate(lastMonth)
        setEndDate(today)
      }
    }
  }
  
  // Xử lý khi thay đổi bộ lọc
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }
  
  // Xử lý khi tạo báo cáo
  const handleGenerateReport = () => {
    // Kiểm tra nếu là custom thì phải có startDate và endDate
    if (timeRange === "custom" && (!startDate || !endDate)) {
      toast.error(t("pleaseSelectDateRange"))
      return
    }
    
    generateReport({
      type: reportType,
      timeRange,
      startDate: startDate ? format(startDate, "yyyy-MM-dd") : undefined,
      endDate: endDate ? format(endDate, "yyyy-MM-dd") : undefined,
      filters
    })
  }
  
  // Xử lý khi xuất báo cáo
  const handleExportReport = () => {
    // Kiểm tra nếu là custom thì phải có startDate và endDate
    if (timeRange === "custom" && (!startDate || !endDate)) {
      toast.error(t("pleaseSelectDateRange"))
      return
    }
    
    // Tạo tên file
    const fileName = `${reportType}_report_${format(new Date(), "yyyy-MM-dd")}.${exportFormat}`
    
    // Thông báo đang xuất báo cáo
    toast.info(t("exportingReport"))
    
    // Gọi API xuất báo cáo
    exportReport({
      type: reportType,
      format: exportFormat,
      timeRange,
      startDate: startDate ? format(startDate, "yyyy-MM-dd") : undefined,
      endDate: endDate ? format(endDate, "yyyy-MM-dd") : undefined,
      filters
    })
    
    // Thông báo xuất báo cáo thành công
    toast.success(t("reportExportedSuccessfully", { fileName }))
  }
  
  // Render các bộ lọc dựa trên loại báo cáo
  const renderFilters = () => {
    switch (reportType) {
      case "revenue":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="text-sm font-medium mb-1 block">{t("orderStatus")}</label>
              <Select
                value={filters.status || "all"}
                onValueChange={(value) => handleFilterChange("status", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("allStatuses")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("allStatuses")}</SelectItem>
                  <SelectItem value="pending">{t("pending")}</SelectItem>
                  <SelectItem value="processing">{t("processing")}</SelectItem>
                  <SelectItem value="shipped">{t("shipped")}</SelectItem>
                  <SelectItem value="delivered">{t("delivered")}</SelectItem>
                  <SelectItem value="cancelled">{t("cancelled")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block">{t("paymentMethod")}</label>
              <Select
                value={filters.paymentMethod || "all"}
                onValueChange={(value) => handleFilterChange("paymentMethod", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("allPaymentMethods")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("allPaymentMethods")}</SelectItem>
                  <SelectItem value="credit_card">{t("creditCard")}</SelectItem>
                  <SelectItem value="bank_transfer">{t("bankTransfer")}</SelectItem>
                  <SelectItem value="cash">{t("cash")}</SelectItem>
                  <SelectItem value="paypal">{t("paypal")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )
      
      case "products":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="text-sm font-medium mb-1 block">{t("category")}</label>
              <Select
                value={filters.category || "all"}
                onValueChange={(value) => handleFilterChange("category", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("allCategories")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("allCategories")}</SelectItem>
                  <SelectItem value="electronics">{t("electronics")}</SelectItem>
                  <SelectItem value="clothing">{t("clothing")}</SelectItem>
                  <SelectItem value="home">{t("home")}</SelectItem>
                  <SelectItem value="beauty">{t("beauty")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )
      
      case "inventory":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="text-sm font-medium mb-1 block">{t("category")}</label>
              <Select
                value={filters.category || "all"}
                onValueChange={(value) => handleFilterChange("category", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("allCategories")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("allCategories")}</SelectItem>
                  <SelectItem value="electronics">{t("electronics")}</SelectItem>
                  <SelectItem value="clothing">{t("clothing")}</SelectItem>
                  <SelectItem value="home">{t("home")}</SelectItem>
                  <SelectItem value="beauty">{t("beauty")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-1 block">{t("stockLevel")}</label>
              <Select
                value={filters.lowStock ? "low" : "all"}
                onValueChange={(value) => handleFilterChange("lowStock", value === "low" ? 10 : undefined)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("allStockLevels")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("allStockLevels")}</SelectItem>
                  <SelectItem value="low">{t("lowStock")}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )
      
      default:
        return null
    }
  }
  
  // Render icon dựa trên loại báo cáo
  const renderReportIcon = () => {
    switch (reportType) {
      case "revenue":
        return <DollarSign className="h-5 w-5" />
      case "products":
        return <BarChart className="h-5 w-5" />
      case "inventory":
        return <Package className="h-5 w-5" />
      default:
        return <FileText className="h-5 w-5" />
    }
  }
  
  return (
    <div className="container py-6">
      <h1 className="text-3xl font-bold mb-6">{t("reports")}</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>{t("generateReport")}</CardTitle>
              <CardDescription>{t("generateReportDescription")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-1 block">{t("reportType")}</label>
                  <Select value={reportType} onValueChange={handleReportTypeChange}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectReportType")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="revenue">{t("revenueReport")}</SelectItem>
                      <SelectItem value="products">{t("productsReport")}</SelectItem>
                      <SelectItem value="inventory">{t("inventoryReport")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <label className="text-sm font-medium mb-1 block">{t("timeRange")}</label>
                  <Select value={timeRange} onValueChange={handleTimeRangeChange}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectTimeRange")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="day">{t("today")}</SelectItem>
                      <SelectItem value="week">{t("thisWeek")}</SelectItem>
                      <SelectItem value="month">{t("thisMonth")}</SelectItem>
                      <SelectItem value="year">{t("thisYear")}</SelectItem>
                      <SelectItem value="custom">{t("customRange")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {timeRange === "custom" && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-1 block">{t("startDate")}</label>
                      <DatePicker
                        selected={startDate}
                        onSelect={setStartDate}
                        maxDate={endDate || new Date()}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-1 block">{t("endDate")}</label>
                      <DatePicker
                        selected={endDate}
                        onSelect={setEndDate}
                        minDate={startDate}
                        maxDate={new Date()}
                      />
                    </div>
                  </div>
                )}
                
                {renderFilters()}
                
                <div>
                  <label className="text-sm font-medium mb-1 block">{t("exportFormat")}</label>
                  <Select value={exportFormat} onValueChange={(value) => setExportFormat(value as "csv" | "json")}>
                    <SelectTrigger>
                      <SelectValue placeholder={t("selectExportFormat")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex flex-col space-y-2 pt-4">
                  <Button onClick={handleGenerateReport} disabled={isGeneratingReport}>
                    {isGeneratingReport ? t("generating") : t("generateReport")}
                  </Button>
                  <Button variant="outline" onClick={handleExportReport} disabled={isExportingReport}>
                    <FileDown className="mr-2 h-4 w-4" />
                    {isExportingReport ? t("exporting") : t("exportReport")}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="lg:col-span-2">
          <Card className="h-full">
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center">
                {renderReportIcon()}
                <CardTitle className="ml-2">
                  {t(`${reportType}Report`)}
                </CardTitle>
              </div>
              {generatedReport && (
                <Button variant="outline" size="sm" onClick={handleExportReport}>
                  <FileDown className="mr-2 h-4 w-4" />
                  {t("export")}
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {isGeneratingReport ? (
                <div className="flex items-center justify-center h-64">
                  <p>{t("generatingReport")}</p>
                </div>
              ) : !generatedReport ? (
                <div className="flex flex-col items-center justify-center h-64 text-center">
                  <FileText className="h-16 w-16 text-muted-foreground mb-4" />
                  <p className="text-lg font-medium">{t("noReportGenerated")}</p>
                  <p className="text-sm text-muted-foreground mt-1">{t("selectOptionsAndGenerate")}</p>
                </div>
              ) : (
                <div className="overflow-auto max-h-[600px]">
                  <ReportContent report={generatedReport} type={reportType} />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

// Component hiển thị nội dung báo cáo
function ReportContent({ report, type }: { report: any, type: string }) {
  const { t } = useTranslation("reports")
  
  // Format tiền tệ
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0
    }).format(value)
  }
  
  // Format ngày
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi })
  }
  
  if (!report || !report.data) {
    return <p>{t("noData")}</p>
  }
  
  switch (type) {
    case "revenue":
      return (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">{t("totalRevenue")}</p>
              <p className="text-2xl font-bold">{formatCurrency(report.data.totalRevenue)}</p>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">{t("totalOrders")}</p>
              <p className="text-2xl font-bold">{report.data.totalOrders}</p>
            </div>
            <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">{t("averageOrderValue")}</p>
              <p className="text-2xl font-bold">{formatCurrency(report.data.averageOrderValue)}</p>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">{t("dailyRevenue")}</h3>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-muted">
                    <th className="p-2 text-left">{t("date")}</th>
                    <th className="p-2 text-right">{t("revenue")}</th>
                    <th className="p-2 text-right">{t("orders")}</th>
                  </tr>
                </thead>
                <tbody>
                  {report.data.dailyRevenue.map((day: any, index: number) => (
                    <tr key={index} className="border-b">
                      <td className="p-2">{formatDate(day.date)}</td>
                      <td className="p-2 text-right">{formatCurrency(day.revenue)}</td>
                      <td className="p-2 text-right">{day.orders}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )
    
    // Các loại báo cáo khác tương tự...
    
    default:
      return <p>{t("unsupportedReportType")}</p>
  }
}
