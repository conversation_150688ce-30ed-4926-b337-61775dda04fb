"use client"

import { useTranslation } from "react-i18next"
import RegisterForm from "@components/auth/register-form"
import Image from "next/image"
import { ModeToggle } from "@components/ui/mode-toggle"
import { LanguageToggle } from "@components/ui/language-toggle"

export default function RegisterPage() {
  const { t } = useTranslation("auth")

  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="absolute right-4 top-4 md:right-8 md:top-8 flex gap-2">
        <ModeToggle />
        <LanguageToggle />
      </div>

      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className="absolute inset-0 bg-zinc-900">
          <Image
            src="/images/auth-background.jpg"
            fill
            alt="Authentication background"
            className="object-cover opacity-20"
            priority
          />
        </div>
        <div className="relative z-20 flex items-center text-lg font-medium">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          ZenBuy
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              {t("register.quote")}
            </p>
            <footer className="text-sm">ZenBuy Team</footer>
          </blockquote>
        </div>
      </div>

      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              {t("register.createAccount")}
            </h1>
            <p className="text-sm text-muted-foreground">
              {t("register.enterDetails")}
            </p>
          </div>
          <RegisterForm />
        </div>
      </div>
    </div>
  )
}