"use client"

import { useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useTranslation } from "react-i18next"
import { AlertCircle, ArrowLeft, RefreshCw, ShoppingBag } from "lucide-react"

import { But<PERSON> } from "@components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@components/ui/card"
import { toast } from "sonner"

export default function PaymentFailedPage() {
  const { t } = useTranslation("checkout")
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const orderId = searchParams.get("orderId")
  const errorCode = searchParams.get("errorCode")
  const errorMessage = searchParams.get("errorMessage")

  useEffect(() => {
    // Hiển thị thông báo lỗi
    if (errorMessage) {
      toast.error(errorMessage)
    }
  }, [errorMessage])

  // <PERSON><PERSON> lý khi người dùng muốn thử lại thanh toán
  const handleRetryPayment = () => {
    if (orderId) {
      router.push(`/checkout/payment?orderId=${orderId}`)
    } else {
      router.push("/checkout")
    }
  }

  // Xử lý khi người dùng muốn quay lại giỏ hàng
  const handleBackToCart = () => {
    router.push("/cart")
  }

  // Xử lý khi người dùng muốn tiếp tục mua sắm
  const handleContinueShopping = () => {
    router.push("/")
  }

  return (
    <div className="container max-w-md py-16">
      <Card className="shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 bg-red-100 dark:bg-red-900/20 w-16 h-16 rounded-full flex items-center justify-center">
            <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-2xl">{t("paymentFailed") || "Thanh toán thất bại"}</CardTitle>
          <CardDescription>
            {t("paymentFailedDesc") || "Đã xảy ra lỗi trong quá trình thanh toán đơn hàng của bạn."}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {errorCode && (
            <div className="bg-muted p-4 rounded-md">
              <p className="text-sm font-medium">{t("errorCode") || "Mã lỗi"}: {errorCode}</p>
              {errorMessage && <p className="text-sm mt-1">{errorMessage}</p>}
            </div>
          )}
          
          <div className="text-sm space-y-2">
            <p>{t("paymentFailedHelp") || "Có một số lý do có thể dẫn đến việc thanh toán thất bại:"}</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>{t("paymentFailedReason1") || "Thông tin thẻ không chính xác"}</li>
              <li>{t("paymentFailedReason2") || "Không đủ số dư trong tài khoản"}</li>
              <li>{t("paymentFailedReason3") || "Ngân hàng từ chối giao dịch vì lý do bảo mật"}</li>
              <li>{t("paymentFailedReason4") || "Lỗi kết nối với cổng thanh toán"}</li>
            </ul>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button className="w-full" onClick={handleRetryPayment}>
            <RefreshCw className="mr-2 h-4 w-4" />
            {t("retryPayment") || "Thử lại thanh toán"}
          </Button>
          <Button variant="outline" className="w-full" onClick={handleBackToCart}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("backToCart") || "Quay lại giỏ hàng"}
          </Button>
          <Button variant="ghost" className="w-full" onClick={handleContinueShopping}>
            <ShoppingBag className="mr-2 h-4 w-4" />
            {t("continueShopping") || "Tiếp tục mua sắm"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
