"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useTranslation } from "react-i18next"
import { toast } from "sonner"

import { But<PERSON> } from "@components/ui/button"
import { useCart } from "@hooks/use-cart"
import { useMutation } from "@tanstack/react-query"
import { ordersService } from "@services/orders.service"
import ShippingAddressForm from "@components/checkout/shipping-address-form"
import PaymentMethodForm from "@components/checkout/payment-method-form"
import OrderSummary from "@components/checkout/order-summary"
import { ShippingAddress, PaymentMethod } from "@/types"

export default function CheckoutPage() {
  const { t } = useTranslation("checkout");
  const router = useRouter();
  const { data: cart } = useCart();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [shippingAddress, setShippingAddress] = useState<ShippingAddress | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(null);

  // Calculate order summary
  const subtotal = cart?.items?.reduce((total, item) => total + item.price * item.quantity, 0) || 0;
  const shipping = subtotal > 0 ? 30000 : 0; // 30.000 VND for shipping
  const total = subtotal + shipping;

  // Create order mutation
  const createOrder = useMutation({
    mutationFn: () => {
      if (!shippingAddress || !paymentMethod) {
        throw new Error("Vui lòng điền đầy đủ thông tin");
      }

      return ordersService.createOrder({
        shippingAddress,
        paymentMethod,
      });
    },
  });

  // Handle shipping address submission
  const handleShippingAddressSubmit = (data: ShippingAddress) => {
    setShippingAddress(data);
  };

  // Handle payment method submission
  const handlePaymentMethodSubmit = (data: { paymentMethod: PaymentMethod }) => {
    setPaymentMethod(data.paymentMethod);
  };

  // Handle order submission
  const handlePlaceOrder = () => {
    if (!cart?.items?.length) {
      toast.error(t("emptyCartError") || "Giỏ hàng của bạn đang trống");
      return;
    }

    if (!shippingAddress) {
      toast.error(t("missingShippingAddress") || "Vui lòng nhập địa chỉ giao hàng");
      return;
    }

    if (!paymentMethod) {
      toast.error(t("missingPaymentMethod") || "Vui lòng chọn phương thức thanh toán");
      return;
    }

    setIsSubmitting(true);

    // Gọi API tạo đơn hàng
    fetch('/api/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        shippingAddress,
        paymentMethod,
      }),
    })
      .then(response => {
        if (!response.ok) {
          return response.json().then(data => {
            throw new Error(data.error || 'Đặt hàng thất bại');
          });
        }
        return response.json();
      })
      .then(data => {
        toast.success(t("orderSuccess") || "Đặt hàng thành công!");
        router.push("/orders");
      })
      .catch(error => {
        toast.error(error.message || t("orderError") || "Đặt hàng thất bại");
        setIsSubmitting(false);
      });
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold mb-8">{t("checkout") || "Thanh toán"}</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          {/* Shipping Address Form */}
          <ShippingAddressForm
            onSubmit={handleShippingAddressSubmit}
            isSubmitting={isSubmitting}
          />

          {/* Payment Method Form */}
          <PaymentMethodForm
            onSubmit={handlePaymentMethodSubmit}
          />

          {/* Place Order Button */}
          <Button
            onClick={handlePlaceOrder}
            className="w-full md:w-auto"
            size="lg"
            disabled={isSubmitting}
          >
            {isSubmitting
              ? (t("placingOrder") || "Đang đặt hàng...")
              : (t("placeOrder") || "Đặt hàng")}
          </Button>
        </div>

        <div className="lg:col-span-1">
          {/* Order Summary */}
          <OrderSummary
            items={cart?.items || []}
            subtotal={subtotal}
            shipping={shipping}
            total={total}
          />
        </div>
      </div>
    </div>
  )
}
