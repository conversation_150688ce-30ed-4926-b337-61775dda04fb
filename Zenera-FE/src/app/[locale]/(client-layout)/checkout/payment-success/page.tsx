"use client"

import { useEffect, useState } from "react"
import { useR<PERSON>er, useSearchParams } from "next/navigation"
import { useTranslation } from "react-i18next"
import { CheckCircle, FileText, ShoppingBag } from "lucide-react"

import { Button } from "@components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@components/ui/card"
import { Skeleton } from "@components/ui/skeleton"
import { toast } from "sonner"

export default function PaymentSuccessPage() {
  const { t } = useTranslation("checkout")
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [isLoading, setIsLoading] = useState(true)
  const [orderDetails, setOrderDetails] = useState<any>(null)
  
  const orderId = searchParams.get("orderId")
  const transactionId = searchParams.get("transactionId")

  useEffect(() => {
    // Nếu có orderId, l<PERSON>y thông tin đơn hàng
    if (orderId) {
      fetchOrderDetails(orderId)
    } else {
      setIsLoading(false)
    }
  }, [orderId])

  // Hàm lấy thông tin đơn hàng
  const fetchOrderDetails = async (id: string) => {
    try {
      const response = await fetch(`/api/orders/${id}`)
      
      if (!response.ok) {
        throw new Error("Failed to fetch order details")
      }
      
      const data = await response.json()
      setOrderDetails(data)
    } catch (error) {
      console.error("Error fetching order details:", error)
      toast.error(t("errorFetchingOrder") || "Lỗi khi lấy thông tin đơn hàng")
    } finally {
      setIsLoading(false)
    }
  }

  // Xử lý khi người dùng muốn xem chi tiết đơn hàng
  const handleViewOrder = () => {
    if (orderId) {
      router.push(`/orders/${orderId}`)
    } else {
      router.push("/orders")
    }
  }

  // Xử lý khi người dùng muốn tiếp tục mua sắm
  const handleContinueShopping = () => {
    router.push("/")
  }

  return (
    <div className="container max-w-md py-16">
      <Card className="shadow-lg">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 bg-green-100 dark:bg-green-900/20 w-16 h-16 rounded-full flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <CardTitle className="text-2xl">{t("paymentSuccess") || "Thanh toán thành công"}</CardTitle>
          <CardDescription>
            {t("paymentSuccessDesc") || "Đơn hàng của bạn đã được thanh toán thành công và đang được xử lý."}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ) : (
            <div className="bg-muted p-4 rounded-md">
              <div className="flex justify-between mb-2">
                <span className="text-sm text-muted-foreground">{t("orderNumber") || "Mã đơn hàng"}:</span>
                <span className="text-sm font-medium">
                  {orderDetails?.orderNumber || orderId || "N/A"}
                </span>
              </div>
              
              {transactionId && (
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-muted-foreground">{t("transactionId") || "Mã giao dịch"}:</span>
                  <span className="text-sm font-medium">{transactionId}</span>
                </div>
              )}
              
              {orderDetails && (
                <>
                  <div className="flex justify-between mb-2">
                    <span className="text-sm text-muted-foreground">{t("total") || "Tổng tiền"}:</span>
                    <span className="text-sm font-medium">
                      {new Intl.NumberFormat("vi-VN", {
                        style: "currency",
                        currency: "VND",
                      }).format(orderDetails.finalAmount || 0)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">{t("paymentMethod") || "Phương thức thanh toán"}:</span>
                    <span className="text-sm font-medium">
                      {orderDetails.paymentMethod === "credit_card"
                        ? t("creditCard") || "Thẻ tín dụng"
                        : orderDetails.paymentMethod === "bank_transfer"
                        ? t("bankTransfer") || "Chuyển khoản ngân hàng"
                        : t("cashOnDelivery") || "Thanh toán khi nhận hàng"}
                    </span>
                  </div>
                </>
              )}
            </div>
          )}
          
          <div className="text-sm space-y-2">
            <p>{t("paymentSuccessHelp") || "Chúng tôi sẽ gửi email xác nhận đơn hàng và thông tin vận chuyển cho bạn trong thời gian sớm nhất."}</p>
            <p>{t("paymentSuccessHelp2") || "Bạn có thể theo dõi trạng thái đơn hàng trong trang Đơn hàng của tôi."}</p>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button className="w-full" onClick={handleViewOrder}>
            <FileText className="mr-2 h-4 w-4" />
            {t("viewOrder") || "Xem đơn hàng"}
          </Button>
          <Button variant="outline" className="w-full" onClick={handleContinueShopping}>
            <ShoppingBag className="mr-2 h-4 w-4" />
            {t("continueShopping") || "Tiếp tục mua sắm"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
