"use client"

import { useTranslation } from "react-i18next"
import { useProfile } from "@hooks/use-auth"
import { AuthGuard } from "@components/auth/auth-guard"
import { BreadcrumbNav } from "@components/ui/breadcrumb-nav"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@components/ui/tabs"
import { Suspense } from "react"
import { Skeleton } from "@components/ui/skeleton"
import { UserProfile } from "@components/profile/user-profile"
import { UserOrders } from "@components/profile/user-orders"
import { UserAddresses } from "@components/profile/user-addresses"
import { UserSettings } from "@components/profile/user-settings"
import { UserAccountSecurity } from "@components/profile/user-account-security"

export default function ProfilePage() {
  const { t } = useTranslation("profile")

  return (
    <AuthGuard>
      <div className="container py-6 md:py-10">
        <BreadcrumbNav
          items={[
            { label: t("breadcrumb.profile"), href: "/profile" },
          ]}
          showHome={true}
          className="mb-6"
        />

        <h1 className="text-3xl font-bold tracking-tight mb-6">{t("title")}</h1>

        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="mb-8">
            <TabsTrigger value="profile">{t("tabs.profile")}</TabsTrigger>
            <TabsTrigger value="orders">{t("tabs.orders")}</TabsTrigger>
            <TabsTrigger value="addresses">{t("tabs.addresses")}</TabsTrigger>
            <TabsTrigger value="security">{t("tabs.security")}</TabsTrigger>
            <TabsTrigger value="settings">{t("tabs.settings")}</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <Suspense fallback={<ProfileSkeleton />}>
              <UserProfile />
            </Suspense>
          </TabsContent>

          <TabsContent value="orders" className="space-y-6">
            <Suspense fallback={<OrdersSkeleton />}>
              <UserOrders />
            </Suspense>
          </TabsContent>

          <TabsContent value="addresses" className="space-y-6">
            <Suspense fallback={<AddressesSkeleton />}>
              <UserAddresses />
            </Suspense>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Suspense fallback={<SecuritySkeleton />}>
              <UserAccountSecurity />
            </Suspense>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Suspense fallback={<SettingsSkeleton />}>
              <UserSettings />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </AuthGuard>
  )
}

function ProfileSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-6">
        <Skeleton className="h-32 w-32 rounded-full" />
        <div className="space-y-4 flex-1">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
          <Skeleton className="h-4 w-1/4" />
        </div>
      </div>
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  )
}

function OrdersSkeleton() {
  return (
    <div className="space-y-6">
      <Skeleton className="h-10 w-full" />
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Skeleton key={i} className="h-24 w-full" />
        ))}
      </div>
    </div>
  )
}

function AddressesSkeleton() {
  return (
    <div className="space-y-6">
      <Skeleton className="h-10 w-full" />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Array.from({ length: 2 }).map((_, i) => (
          <Skeleton key={i} className="h-40 w-full" />
        ))}
      </div>
    </div>
  )
}

function SecuritySkeleton() {
  return (
    <div className="space-y-6">
      <Skeleton className="h-10 w-full" />
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  )
}

function SettingsSkeleton() {
  return (
    <div className="space-y-6">
      <Skeleton className="h-10 w-full" />
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  )
}
