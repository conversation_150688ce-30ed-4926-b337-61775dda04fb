import { fetchApi } from "./api"

export interface Notification {
  _id: string
  user: string
  title: string
  message: string
  type: "order" | "inventory" | "payment" | "system"
  read: boolean
  data?: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface NotificationSettings {
  notifications: boolean
  orderNotifications: boolean
  inventoryNotifications: boolean
  paymentNotifications: boolean
  systemNotifications: boolean
}

export interface NotificationsResponse {
  notifications: Notification[]
  unreadCount: number
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

export const notificationsService = {
  // L<PERSON>y danh sách thông báo
  getNotifications: (
    page = 1,
    limit = 10,
    read: "true" | "false" | "all" = "all",
    type: "order" | "inventory" | "payment" | "system" | "all" = "all"
  ) =>
    fetchApi<NotificationsResponse>(
      `/notifications?page=${page}&limit=${limit}&read=${read}&type=${type}`
    ),

  // <PERSON><PERSON><PERSON> dấu thông báo đã đọc
  markAsRead: (id: string) => fetchApi<Notification>(`/notifications/${id}/read`, { method: "PUT" }),

  // <PERSON><PERSON>h dấu tất cả thông báo đã đọc
  markAllAsRead: () => fetchApi<{ success: boolean; modifiedCount: number }>(`/notifications/read-all`, { method: "PUT" }),

  // Lấy cài đặt thông báo
  getSettings: () => fetchApi<NotificationSettings>(`/notifications/settings`),

  // Cập nhật cài đặt thông báo
  updateSettings: (settings: Partial<NotificationSettings>) =>
    fetchApi<NotificationSettings>(`/notifications/settings`, {
      method: "PUT",
      body: JSON.stringify(settings),
    }),
}
