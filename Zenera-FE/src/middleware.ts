// Middleware xác thực và i18n routing
// https://nextjs.org/docs/app/building-your-application/routing/middleware

import { i18nRouter } from 'next-i18n-router';
import i18nConfig from '../i18nConfig';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from './lib/jwt';

// Danh sách các API cần xác thực
const protectedApiRoutes = [
  // "/api/cart",
  // "/api/orders",
  // "/api/analytics",
  // "/api/inventory",
  // "/api/shipping-addresses",
  // "/api/auth/profile",
  // "/api/auth/change-password",
  // "/api/seller",
];

// Danh sách các trang client cần xác thực
const protectedClientRoutes = [
  // "/profile",
  // "/cart",
  // "/checkout",
  // "/orders",
  // "/seller",
];

// Danh sách các trang chỉ dành cho seller
const sellerOnlyRoutes = [
  // "/seller",
];

// Danh sách các trang chỉ dành cho admin
const adminOnlyRoutes = [
  // "/admin",
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const isApiRoute = pathname.startsWith('/api');

  // Xử lý xác thực cho API routes
  if (isApiRoute) {
    console.log('Middleware - API route detected:', pathname);

    // Kiểm tra nếu là protected API route
    if (protectedApiRoutes.some(route => pathname.startsWith(route))) {
      // Lấy token từ header hoặc cookie
      const authHeader = request.headers.get("Authorization");
      let token = authHeader ? authHeader.replace('Bearer ', '') : null;

      if (!token) {
        // Thử lấy từ cookie
        const cookieHeader = request.cookies.get('token');
        token = cookieHeader?.value;
      }

      if (!token) {
        console.log('Middleware - Protected API route without token');
        return NextResponse.json(
          { error: "Không có quyền truy cập", message: "Authentication required" },
          { status: 401 }
        );
      }

      // Xác thực token
      const decoded = verifyToken(token);
      if (!decoded) {
        console.log('Middleware - Invalid or expired token');
        return NextResponse.json(
          { error: "Token không hợp lệ hoặc đã hết hạn", message: "Token expired" },
          { status: 401 }
        );
      }

      // Kiểm tra quyền truy cập cho các route chỉ dành cho seller
      if (pathname.startsWith('/api/seller') && decoded.role !== 'seller' && decoded.role !== 'admin') {
        console.log('Middleware - Seller route accessed by non-seller');
        return NextResponse.json(
          { error: "Không có quyền truy cập", message: "Seller access required" },
          { status: 403 }
        );
      }

      // Kiểm tra quyền truy cập cho các route chỉ dành cho admin
      if (pathname.startsWith('/api/admin') && decoded.role !== 'admin') {
        console.log('Middleware - Admin route accessed by non-admin');
        return NextResponse.json(
          { error: "Không có quyền truy cập", message: "Admin access required" },
          { status: 403 }
        );
      }
    }

    // Đối với API routes, chỉ kiểm tra xác thực và không áp dụng i18n routing
    return NextResponse.next();
  }

  // Xử lý xác thực cho client routes
  // Lưu ý: Đây chỉ là kiểm tra sơ bộ, xác thực chính xác sẽ được thực hiện ở client
  const localePrefix = `/${request.nextUrl.pathname.split('/')[1]}`;

  // Kiểm tra nếu là protected client route
  const isProtectedClientRoute = protectedClientRoutes.some(route =>
    pathname.replace(localePrefix, '').startsWith(route)
  );

  if (isProtectedClientRoute) {
    // Lấy token từ cookie
    const token = request.cookies.get('token')?.value;

    if (!token) {
      // Lưu URL hiện tại để redirect sau khi đăng nhập
      const url = new URL(`${localePrefix}/login`, request.url);
      url.searchParams.set('callbackUrl', request.url);

      console.log('Middleware - Redirecting to login from:', pathname);
      return NextResponse.redirect(url);
    }

    // Xác thực token
    const decoded = verifyToken(token);
    if (!decoded) {
      // Token không hợp lệ hoặc đã hết hạn
      const url = new URL(`${localePrefix}/login`, request.url);
      url.searchParams.set('callbackUrl', request.url);
      url.searchParams.set('tokenExpired', 'true');

      console.log('Middleware - Token expired, redirecting to login');
      return NextResponse.redirect(url);
    }

    // Kiểm tra quyền truy cập cho các route chỉ dành cho seller
    const isSellerRoute = sellerOnlyRoutes.some(route =>
      pathname.replace(localePrefix, '').startsWith(route)
    );

    if (isSellerRoute && decoded.role !== 'seller' && decoded.role !== 'admin') {
      console.log('Middleware - Seller route accessed by non-seller');
      return NextResponse.redirect(new URL(`${localePrefix}`, request.url));
    }

    // Kiểm tra quyền truy cập cho các route chỉ dành cho admin
    const isAdminRoute = adminOnlyRoutes.some(route =>
      pathname.replace(localePrefix, '').startsWith(route)
    );

    if (isAdminRoute && decoded.role !== 'admin') {
      console.log('Middleware - Admin route accessed by non-admin');
      return NextResponse.redirect(new URL(`${localePrefix}`, request.url));
    }
  }

  // Xử lý i18n cho các routes không phải API
  console.log('Middleware - Applying i18n routing for:', pathname);
  return i18nRouter(request, i18nConfig);
}

// Áp dụng middleware cho cả API routes và i18n routing
export const config = {
  matcher: [
    // API routes cần xác thực
    "/api/cart/:path*",
    "/api/orders/:path*",
    "/api/analytics/:path*",
    "/api/inventory/:path*",
    "/api/shipping-addresses/:path*",
    "/api/auth/profile/:path*",
    "/api/auth/change-password/:path*",
    "/api/seller/:path*",
    // Tất cả các API routes khác
    "/api/:path*",
    // i18n routes - loại trừ API routes và static files
    '/((?!api|static|.*\\..*|_next).*)'
  ]
};
