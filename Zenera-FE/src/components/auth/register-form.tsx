"use client"

import type React from "react"
import { useState } from "react"
import { useRegister } from "@hooks/use-auth"
import { <PERSON><PERSON> } from "@components/ui/button"
import { Input } from "@components/ui/input"
import { useRouter, useSearchParams } from "next/navigation"
import { useTranslation } from "react-i18next"
import { Eye, EyeOff, Mail, Lock, User, AlertCircle } from "lucide-react"
import { toast } from "sonner"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Checkbox } from "@components/ui/checkbox"

const formSchema = z.object({
  name: z.string().min(2, {
    message: "<PERSON><PERSON> tên phải có ít nhất 2 ký tự.",
  }),
  email: z.string().email({
    message: "<PERSON><PERSON> không hợp lệ.",
  }),
  password: z.string().min(6, {
    message: "Mật khẩu phải có ít nhất 6 ký tự.",
  }),
  confirmPassword: z.string().min(6, {
    message: "Xác nhận mật khẩu phải có ít nhất 6 ký tự.",
  }),
  role: z.enum(["customer", "seller"]).default("customer"),
  agreeTerms: z.boolean().refine(val => val === true, {
    message: "Bạn phải đồng ý với điều khoản dịch vụ.",
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Mật khẩu xác nhận không khớp.",
  path: ["confirmPassword"],
});

export default function RegisterForm() {
  const { t } = useTranslation("auth")
  const register = useRegister()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // Lấy callbackUrl từ query params nếu có
  const callbackUrl = searchParams.get("callbackUrl") || "/"

  // Khởi tạo form với react-hook-form và zod validation
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      role: "customer",
      agreeTerms: false,
    },
  })

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      await register.mutateAsync({
        name: values.name,
        email: values.email,
        password: values.password,
        role: values.role,
      })

      // Hiển thị thông báo thành công
      toast.success(t("register.success"))

      // Redirect sau khi đăng ký thành công
      router.push(callbackUrl)
    } catch (error) {
      // Lỗi đã được xử lý bởi mutation
      console.error("Registration failed")
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl">{t("register.createAccount")}</CardTitle>
        <CardDescription>
          {t("register.enterDetails")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("register.nameLabel")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t("register.namePlaceholder")}
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("register.emailLabel")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t("register.emailPlaceholder")}
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("register.passwordLabel")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder={t("register.passwordPlaceholder")}
                        className="pl-10"
                        {...field}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                        <span className="sr-only">
                          {showPassword ? t("register.hidePassword") : t("register.showPassword")}
                        </span>
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("register.confirmPasswordLabel")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder={t("register.confirmPasswordPlaceholder")}
                        className="pl-10"
                        {...field}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                        <span className="sr-only">
                          {showConfirmPassword ? t("register.hidePassword") : t("register.showPassword")}
                        </span>
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("register.roleLabel")}</FormLabel>
                  <div className="flex gap-4">
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <input
                        type="radio"
                        id="customer"
                        value="customer"
                        checked={field.value === "customer"}
                        onChange={() => field.onChange("customer")}
                        className="h-4 w-4"
                      />
                      <label htmlFor="customer" className="text-sm font-normal">
                        {t("register.customerRole")}
                      </label>
                    </FormItem>
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <input
                        type="radio"
                        id="seller"
                        value="seller"
                        checked={field.value === "seller"}
                        onChange={() => field.onChange("seller")}
                        className="h-4 w-4"
                      />
                      <label htmlFor="seller" className="text-sm font-normal">
                        {t("register.sellerRole")}
                      </label>
                    </FormItem>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="agreeTerms"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="text-sm font-normal">
                      {t("register.agreeTerms")}{" "}
                      <a href="/terms" className="text-primary hover:underline">
                        {t("register.termsLink")}
                      </a>
                    </FormLabel>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />

            {register.error && (
              <div className="flex items-center gap-2 rounded-md bg-destructive/15 p-3 text-sm text-destructive">
                <AlertCircle className="h-4 w-4" />
                <p>{register.error instanceof Error ? register.error.message : String(register.error)}</p>
              </div>
            )}

            <Button type="submit" className="w-full" disabled={register.isPending}>
              {register.isPending ? t("register.registering") : t("register.registerButton")}
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex flex-col items-center justify-center space-y-2">
        <div className="text-sm text-muted-foreground">
          {t("register.haveAccount")}{" "}
          <Button variant="link" className="p-0 text-sm" asChild>
            <a href="/login">{t("register.login")}</a>
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}