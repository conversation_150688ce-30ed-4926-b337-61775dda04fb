"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useTranslation } from "react-i18next"
import { useQuery } from "@tanstack/react-query"

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form"
import { Input } from "@components/ui/input"
import { Button } from "@components/ui/button"
import { Checkbox } from "@components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select"
import { Separator } from "@components/ui/separator"
import { ShippingAddress } from "@/types"

// Tạo service cho shipping addresses
const shippingAddressesService = {
  getAddresses: async () => {
    const response = await fetch('/api/shipping-addresses', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch shipping addresses');
    }

    return response.json();
  },
};

const formSchema = z.object({
  fullName: z.string().min(2, {
    message: "Tên người nhận phải có ít nhất 2 ký tự.",
  }),
  address: z.string().min(5, {
    message: "Địa chỉ phải có ít nhất 5 ký tự.",
  }),
  city: z.string().min(2, {
    message: "Tên thành phố phải có ít nhất 2 ký tự.",
  }),
  state: z.string().min(2, {
    message: "Tên tỉnh/thành phố phải có ít nhất 2 ký tự.",
  }),
  country: z.string().min(2, {
    message: "Tên quốc gia phải có ít nhất 2 ký tự.",
  }),
  zipCode: z.string().min(2, {
    message: "Mã bưu điện phải có ít nhất 2 ký tự.",
  }),
  phone: z.string().min(10, {
    message: "Số điện thoại phải có ít nhất 10 ký tự.",
  }),
  saveAddress: z.boolean().optional(),
  setAsDefault: z.boolean().optional(),
  selectedAddressId: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>

interface ShippingAddressFormProps {
  onSubmit: (data: ShippingAddress) => void;
  isSubmitting?: boolean;
}

export default function ShippingAddressForm({ onSubmit, isSubmitting = false }: ShippingAddressFormProps) {
  const { t } = useTranslation("checkout");
  const [useExistingAddress, setUseExistingAddress] = useState(false);

  // Fetch saved addresses
  const { data: addressesData, isLoading } = useQuery({
    queryKey: ["shippingAddresses"],
    queryFn: () => shippingAddressesService.getAddresses(),
  });

  const savedAddresses = addressesData?.addresses || [];
  const hasAddresses = savedAddresses.length > 0;

  // Form definition
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      address: "",
      city: "",
      state: "",
      country: "",
      zipCode: "",
      phone: "",
      saveAddress: false,
      setAsDefault: false,
      selectedAddressId: "",
    },
  });

  // Handle address selection
  const handleAddressSelection = (addressId: string) => {
    const selectedAddress = savedAddresses.find(addr => addr._id === addressId);
    if (selectedAddress) {
      form.setValue("fullName", selectedAddress.fullName);
      form.setValue("address", selectedAddress.address);
      form.setValue("city", selectedAddress.city);
      form.setValue("state", selectedAddress.state);
      form.setValue("country", selectedAddress.country);
      form.setValue("zipCode", selectedAddress.zipCode);
      form.setValue("phone", selectedAddress.phone);
    }
  };

  // Form submission handler
  const handleSubmit = (data: FormValues) => {
    const shippingAddress: ShippingAddress = {
      fullName: data.fullName,
      address: data.address,
      city: data.city,
      state: data.state,
      country: data.country,
      zipCode: data.zipCode,
      phone: data.phone,
    };

    // Add metadata for saving address
    if (data.saveAddress) {
      (shippingAddress as any).saveAddress = true;
      (shippingAddress as any).setAsDefault = data.setAsDefault;
    }

    onSubmit(shippingAddress);
  };

  // Submit form when component mounts with default values
  useEffect(() => {
    // Only submit if form is valid
    const isValid = form.formState.isValid;
    if (isValid && form.getValues().fullName) {
      handleSubmit(form.getValues());
    }
  }, [form.formState.isValid]);

  // Toggle between new and existing address
  useEffect(() => {
    if (!hasAddresses) {
      setUseExistingAddress(false);
    }
  }, [hasAddresses]);

  return (
    <div className="bg-card rounded-lg p-6 shadow-sm">
      <h2 className="text-xl font-semibold mb-4">{t("shippingAddress") || "Địa chỉ giao hàng"}</h2>

      {hasAddresses && (
        <div className="mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <Button
              type="button"
              variant={useExistingAddress ? "default" : "outline"}
              size="sm"
              onClick={() => setUseExistingAddress(true)}
            >
              {t("useExistingAddress") || "Dùng địa chỉ đã lưu"}
            </Button>
            <Button
              type="button"
              variant={!useExistingAddress ? "default" : "outline"}
              size="sm"
              onClick={() => setUseExistingAddress(false)}
            >
              {t("addNewAddress") || "Thêm địa chỉ mới"}
            </Button>
          </div>

          {useExistingAddress && (
            <div className="mb-4">
              <FormField
                control={form.control}
                name="selectedAddressId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("selectAddress") || "Chọn địa chỉ"}</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleAddressSelection(value);
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={t("selectAddress") || "Chọn địa chỉ"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {savedAddresses.map((address) => (
                          <SelectItem key={address._id} value={address._id}>
                            {address.fullName} - {address.address}, {address.city}
                            {address.isDefault && ` (${t("default") || "Mặc định"})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Separator className="my-4" />
            </div>
          )}
        </div>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("fullName") || "Họ tên"}</FormLabel>
                  <FormControl>
                    <Input placeholder="Nguyễn Văn A" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("phone") || "Số điện thoại"}</FormLabel>
                  <FormControl>
                    <Input placeholder="0912345678" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("address") || "Địa chỉ"}</FormLabel>
                <FormControl>
                  <Input placeholder="123 Đường ABC" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("city") || "Thành phố"}</FormLabel>
                  <FormControl>
                    <Input placeholder="Hà Nội" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("state") || "Tỉnh/Thành phố"}</FormLabel>
                  <FormControl>
                    <Input placeholder="Hà Nội" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="zipCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("zipCode") || "Mã bưu điện"}</FormLabel>
                  <FormControl>
                    <Input placeholder="100000" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("country") || "Quốc gia"}</FormLabel>
                  <FormControl>
                    <Input placeholder="Việt Nam" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {!useExistingAddress && (
            <div className="space-y-2">
              <FormField
                control={form.control}
                name="saveAddress"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        {t("saveAddress") || "Lưu địa chỉ này cho lần sau"}
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              {form.watch("saveAddress") && (
                <FormField
                  control={form.control}
                  name="setAsDefault"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 ml-6">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          {t("setAsDefault") || "Đặt làm địa chỉ mặc định"}
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              )}
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}
