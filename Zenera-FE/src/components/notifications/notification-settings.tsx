"use client"

import { useNotificationSettings } from "@hooks/use-notifications"
import { useNotificationsState } from "@store/notifications/notifications.state"
import { useTranslation } from "react-i18next"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Switch } from "@components/ui/switch"
import { Label } from "@components/ui/label"
import { Skeleton } from "@components/ui/skeleton"
import { Bell, ShoppingCart, Package, CreditCard, Info } from "lucide-react"

export default function NotificationSettings() {
  const { t } = useTranslation("notifications")
  const { isLoading, updateSettings } = useNotificationSettings()
  const notificationsState = useNotificationsState()

  // Xử lý khi thay đổi cài đặt
  const handleSettingChange = (key: string, value: boolean) => {
    // Nếu tắt thông báo chính, tắt tất cả các loại thông báo
    if (key === "notifications" && !value) {
      updateSettings({
        notifications: false,
        orderNotifications: false,
        inventoryNotifications: false,
        paymentNotifications: false,
        systemNotifications: false,
      })
      return
    }

    // Nếu bật một loại thông báo, đảm bảo thông báo chính được bật
    if (key !== "notifications" && value) {
      updateSettings({
        [key]: value,
        notifications: true,
      })
      return
    }

    // Cập nhật cài đặt
    updateSettings({
      [key]: value,
    })
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-40" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-full" />
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between">
              <Skeleton className="h-4 w-40" />
              <Skeleton className="h-6 w-10" />
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("notificationSettings")}</CardTitle>
        <CardDescription>{t("notificationSettingsDescription")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            <Label htmlFor="notifications">{t("allNotifications")}</Label>
          </div>
          <Switch
            id="notifications"
            checked={notificationsState.settings.get().notifications}
            onCheckedChange={(checked) => handleSettingChange("notifications", checked)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            <Label htmlFor="orderNotifications">{t("orderNotifications")}</Label>
          </div>
          <Switch
            id="orderNotifications"
            checked={notificationsState.settings.get().orderNotifications}
            disabled={!notificationsState.settings.get().notifications}
            onCheckedChange={(checked) => handleSettingChange("orderNotifications", checked)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            <Label htmlFor="inventoryNotifications">{t("inventoryNotifications")}</Label>
          </div>
          <Switch
            id="inventoryNotifications"
            checked={notificationsState.settings.get().inventoryNotifications}
            disabled={!notificationsState.settings.get().notifications}
            onCheckedChange={(checked) => handleSettingChange("inventoryNotifications", checked)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            <Label htmlFor="paymentNotifications">{t("paymentNotifications")}</Label>
          </div>
          <Switch
            id="paymentNotifications"
            checked={notificationsState.settings.get().paymentNotifications}
            disabled={!notificationsState.settings.get().notifications}
            onCheckedChange={(checked) => handleSettingChange("paymentNotifications", checked)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            <Label htmlFor="systemNotifications">{t("systemNotifications")}</Label>
          </div>
          <Switch
            id="systemNotifications"
            checked={notificationsState.settings.get().systemNotifications}
            disabled={!notificationsState.settings.get().notifications}
            onCheckedChange={(checked) => handleSettingChange("systemNotifications", checked)}
          />
        </div>
      </CardContent>
    </Card>
  )
}
