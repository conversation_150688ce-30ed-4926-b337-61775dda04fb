"use client"

import { useState, useEffect } from "react"
import { Star, MessageSquare, <PERSON>humbsUp, Filter, ChevronDown, Loader2 } from "lucide-react"
import { useTranslation } from "react-i18next"
import { format } from "date-fns"
import { Avatar, AvatarFallback, AvatarImage } from "@components/ui/avatar"
import { Button } from "@components/ui/button"
import { Badge } from "@components/ui/badge"
import { Skeleton } from "@components/ui/skeleton"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@components/ui/pagination"

interface Review {
  _id: string
  user: {
    _id: string
    name: string
    avatar?: string
  }
  rating: number
  comment: string
  createdAt: string
}

interface ProductReviewsProps {
  productId: string
}

export default function ProductReviews({ productId }: ProductReviewsProps) {
  const { t } = useTranslation("detail-product")
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [sort, setSort] = useState("newest")
  const [stats, setStats] = useState({
    average: 0,
    total: 0,
    distribution: [0, 0, 0, 0, 0] // 5 stars to 1 star
  })

  useEffect(() => {
    const fetchReviews = async () => {
      if (!productId) return

      try {
        setLoading(true)
        setError(null)

        // Build query parameters
        const params = new URLSearchParams()
        params.append("page", page.toString())
        params.append("limit", "5")
        params.append("sort", sort)

        const response = await fetch(`/api/products/${productId}/reviews?${params.toString()}`)

        if (!response.ok) {
          throw new Error("Failed to fetch reviews")
        }

        const data = await response.json()
        setReviews(data.reviews || [])
        setTotalPages(data.totalPages || 1)

        // Calculate stats
        if (data.reviews && data.reviews.length > 0) {
          const total = data.total || data.reviews.length
          const sum = data.reviews.reduce((acc: number, review: Review) => acc + review.rating, 0)
          const avg = sum / data.reviews.length

          // Calculate distribution
          const dist = [0, 0, 0, 0, 0]
          data.reviews.forEach((review: Review) => {
            if (review.rating >= 1 && review.rating <= 5) {
              dist[5 - review.rating]++
            }
          })

          setStats({
            average: parseFloat(avg.toFixed(1)),
            total,
            distribution: dist
          })
        }
      } catch (error) {
        console.error("Error fetching reviews:", error)
        setError(error instanceof Error ? error.message : "An unknown error occurred")
      } finally {
        setLoading(false)
      }
    }

    fetchReviews()
  }, [productId, page, sort])

  const handleSortChange = (value: string) => {
    setSort(value)
    setPage(1) // Reset to first page when changing sort
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {Array.from({ length: 5 }).map((_, index) => (
          <Star
            key={index}
            className={`h-4 w-4 ${index < rating ? "text-yellow-400" : "text-gray-300"}`}
            fill="currentColor"
          />
        ))}
      </div>
    )
  }

  const renderRatingBar = (count: number, total: number, stars: number) => {
    const percentage = total > 0 ? (count / total) * 100 : 0

    return (
      <div className="flex items-center gap-2 mb-1">
        <div className="flex items-center w-16">
          <span className="text-sm font-medium">{stars} stars</span>
        </div>
        <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
          <div
            className="h-full bg-yellow-400 rounded-full"
            style={{ width: `${percentage}%` }}
          />
        </div>
        <div className="w-10 text-right text-sm text-gray-500">
          {percentage.toFixed(0)}%
        </div>
      </div>
    )
  }

  const renderPagination = () => {
    if (totalPages <= 1) return null

    return (
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
            />
          </PaginationItem>

          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // Show pages around current page
            let pageNum
            if (totalPages <= 5) {
              pageNum = i + 1
            } else if (page <= 3) {
              pageNum = i + 1
            } else if (page >= totalPages - 2) {
              pageNum = totalPages - 4 + i
            } else {
              pageNum = page - 2 + i
            }

            return (
              <PaginationItem key={pageNum}>
                <PaginationLink
                  isActive={page === pageNum}
                  onClick={() => setPage(pageNum)}
                >
                  {pageNum}
                </PaginationLink>
              </PaginationItem>
            )
          })}

          <PaginationItem>
            <PaginationNext
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    )
  }

  return (
    <div className="mt-12">
      <h2 className="text-2xl font-bold mb-6">{t("customerReviews")}</h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        {/* Rating summary */}
        <div className="col-span-1">
          <div className="bg-gray-50 p-6 rounded-lg">
            <div className="flex flex-col items-center mb-4">
              <div className="text-4xl font-bold">{stats.average}</div>
              <div className="flex mt-2 mb-1">
                {renderStars(stats.average)}
              </div>
              <div className="text-sm text-gray-500">
                {t("basedOnReviews", { count: stats.total }) || `Based on ${stats.total} reviews`}
              </div>
            </div>

            <div className="mt-6">
              {stats.distribution.map((count, index) => (
                <div key={5 - index}>
                  {renderRatingBar(count, stats.total, 5 - index)}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Reviews list */}
        <div className="col-span-1 md:col-span-2">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium">
              {stats.total > 0
                ? t("reviewsCount", { count: stats.total }) || `${stats.total} Reviews`
                : t("noReviewsYet") || "No Reviews Yet"}
            </h3>

            <Select value={sort} onValueChange={handleSortChange}>
              <SelectTrigger className="w-[180px]">
                <div className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder={t("sortBy") || "Sort by"} />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">{t("newest") || "Newest"}</SelectItem>
                <SelectItem value="oldest">{t("oldest") || "Oldest"}</SelectItem>
                <SelectItem value="highest">{t("highestRated") || "Highest Rated"}</SelectItem>
                <SelectItem value="lowest">{t("lowestRated") || "Lowest Rated"}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="space-y-6">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="border-b pb-6">
                  <div className="flex items-start gap-4">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8 border border-dashed rounded-lg">
              <MessageSquare className="h-10 w-10 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">{error}</p>
            </div>
          ) : reviews.length === 0 ? (
            <div className="text-center py-8 border border-dashed rounded-lg">
              <MessageSquare className="h-10 w-10 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">{t("noReviewsYet") || "No reviews yet. Be the first to review this product!"}</p>
            </div>
          ) : (
            <div className="space-y-6">
              {reviews.map((review) => (
                <div key={review._id} className="border-b pb-6">
                  <div className="flex items-start gap-4">
                    <Avatar>
                      <AvatarImage src={review.user.avatar} alt={review.user.name} />
                      <AvatarFallback>{review.user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex flex-wrap items-center gap-2">
                        <h4 className="font-medium">{review.user.name}</h4>
                        <Badge variant="outline" className="font-normal">
                          {t("verifiedPurchase") || "Verified Purchase"}
                        </Badge>
                      </div>
                      <div className="flex items-center mt-1 mb-2">
                        {renderStars(review.rating)}
                        <span className="ml-2 text-sm text-gray-500">
                          {format(new Date(review.createdAt), 'MMM d, yyyy')}
                        </span>
                      </div>
                      <p className="text-gray-700">{review.comment}</p>
                      <div className="flex items-center mt-3">
                        <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
                          <ThumbsUp className="h-4 w-4 mr-1" />
                          {t("helpful") || "Helpful"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              <div className="mt-6">
                {renderPagination()}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

