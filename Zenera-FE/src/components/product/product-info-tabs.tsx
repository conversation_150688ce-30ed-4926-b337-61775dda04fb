"use client"

import { useState } from "react"
import { useTranslation } from "react-i18next"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@components/ui/tabs"
import { FileText, Settings } from "lucide-react"
import ProductSpecifications from "./product-specifications"

interface ProductInfoTabsProps {
  description: string
  specifications?: Record<string, string>
}

export default function ProductInfoTabs({ description, specifications }: ProductInfoTabsProps) {
  const { t } = useTranslation("detail-product")
  const [activeTab, setActiveTab] = useState("description")

  return (
    <div className="mt-12">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8">
          <TabsTrigger value="description" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>{t("description") || "Description"}</span>
          </TabsTrigger>
          <TabsTrigger value="specifications" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span>{t("specifications") || "Specifications"}</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="description" className="mt-0">
          <div className="prose max-w-none">
            {description ? (
              <div dangerouslySetInnerHTML={{ __html: description }} />
            ) : (
              <p className="text-gray-500 italic">{t("noDescription") || "No description available for this product."}</p>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="specifications" className="mt-0">
          <ProductSpecifications specifications={specifications} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
