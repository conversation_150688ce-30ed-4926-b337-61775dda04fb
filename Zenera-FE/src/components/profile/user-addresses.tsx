"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { But<PERSON> } from "@components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@components/ui/card"
import { toast } from "sonner"
import { MapPin, Plus, Edit, Trash, Check, Home } from "lucide-react"
import { Badge } from "@components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@components/ui/dialog"
import { Input } from "@components/ui/input"
import { Label } from "@components/ui/label"
import { Checkbox } from "@components/ui/checkbox"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@components/ui/form"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@components/ui/alert-dialog"

interface ShippingAddress {
  _id: string
  fullName: string
  phoneNumber: string
  addressLine1: string
  addressLine2?: string
  city: string
  district: string
  ward: string
  postalCode?: string
  isDefault: boolean
}

const addressSchema = z.object({
  fullName: z.string().min(2, {
    message: "Họ tên phải có ít nhất 2 ký tự.",
  }),
  phoneNumber: z.string().min(10, {
    message: "Số điện thoại không hợp lệ.",
  }),
  addressLine1: z.string().min(5, {
    message: "Địa chỉ phải có ít nhất 5 ký tự.",
  }),
  addressLine2: z.string().optional(),
  city: z.string().min(2, {
    message: "Thành phố không hợp lệ.",
  }),
  district: z.string().min(2, {
    message: "Quận/Huyện không hợp lệ.",
  }),
  ward: z.string().min(2, {
    message: "Phường/Xã không hợp lệ.",
  }),
  postalCode: z.string().optional(),
  isDefault: z.boolean().default(false),
});

export function UserAddresses() {
  const { t } = useTranslation("profile")
  const [addresses, setAddresses] = useState<ShippingAddress[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [currentAddress, setCurrentAddress] = useState<ShippingAddress | null>(null)
  
  const form = useForm<z.infer<typeof addressSchema>>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      fullName: "",
      phoneNumber: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      district: "",
      ward: "",
      postalCode: "",
      isDefault: false,
    },
  })
  
  // Tải danh sách địa chỉ
  const fetchAddresses = async () => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/shipping-addresses")
      if (!response.ok) {
        throw new Error("Không thể tải danh sách địa chỉ")
      }
      const data = await response.json()
      setAddresses(data)
    } catch (error) {
      toast.error("Lỗi khi tải danh sách địa chỉ")
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }
  
  useEffect(() => {
    fetchAddresses()
  }, [])
  
  // Xử lý thêm địa chỉ mới
  const handleAddAddress = async (values: z.infer<typeof addressSchema>) => {
    try {
      const response = await fetch("/api/shipping-addresses", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "Lỗi khi thêm địa chỉ")
      }
      
      toast.success("Đã thêm địa chỉ mới")
      setIsAddDialogOpen(false)
      form.reset()
      fetchAddresses()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Lỗi khi thêm địa chỉ")
    }
  }
  
  // Xử lý cập nhật địa chỉ
  const handleEditAddress = async (values: z.infer<typeof addressSchema>) => {
    if (!currentAddress) return
    
    try {
      const response = await fetch(`/api/shipping-addresses/${currentAddress._id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "Lỗi khi cập nhật địa chỉ")
      }
      
      toast.success("Đã cập nhật địa chỉ")
      setIsEditDialogOpen(false)
      setCurrentAddress(null)
      fetchAddresses()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Lỗi khi cập nhật địa chỉ")
    }
  }
  
  // Xử lý xóa địa chỉ
  const handleDeleteAddress = async (id: string) => {
    try {
      const response = await fetch(`/api/shipping-addresses/${id}`, {
        method: "DELETE",
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "Lỗi khi xóa địa chỉ")
      }
      
      toast.success("Đã xóa địa chỉ")
      fetchAddresses()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Lỗi khi xóa địa chỉ")
    }
  }
  
  // Xử lý đặt địa chỉ mặc định
  const handleSetDefaultAddress = async (id: string) => {
    try {
      const response = await fetch(`/api/shipping-addresses/${id}/set-default`, {
        method: "PUT",
      })
      
      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || "Lỗi khi đặt địa chỉ mặc định")
      }
      
      toast.success("Đã đặt địa chỉ mặc định")
      fetchAddresses()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Lỗi khi đặt địa chỉ mặc định")
    }
  }
  
  // Mở dialog chỉnh sửa địa chỉ
  const openEditDialog = (address: ShippingAddress) => {
    setCurrentAddress(address)
    form.reset({
      fullName: address.fullName,
      phoneNumber: address.phoneNumber,
      addressLine1: address.addressLine1,
      addressLine2: address.addressLine2 || "",
      city: address.city,
      district: address.district,
      ward: address.ward,
      postalCode: address.postalCode || "",
      isDefault: address.isDefault,
    })
    setIsEditDialogOpen(true)
  }
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <MapPin className="mr-2 h-5 w-5" />
            <div>
              <CardTitle>{t("addresses.title")}</CardTitle>
              <CardDescription>{t("addresses.description")}</CardDescription>
            </div>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                {t("addresses.addNew")}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>{t("addresses.addNewTitle")}</DialogTitle>
                <DialogDescription>{t("addresses.addNewDescription")}</DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleAddAddress)} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="fullName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("addresses.fullName")}</FormLabel>
                          <FormControl>
                            <Input placeholder={t("addresses.fullNamePlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("addresses.phoneNumber")}</FormLabel>
                          <FormControl>
                            <Input placeholder={t("addresses.phoneNumberPlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="addressLine1"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("addresses.addressLine1")}</FormLabel>
                        <FormControl>
                          <Input placeholder={t("addresses.addressLine1Placeholder")} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="addressLine2"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("addresses.addressLine2")}</FormLabel>
                        <FormControl>
                          <Input placeholder={t("addresses.addressLine2Placeholder")} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("addresses.city")}</FormLabel>
                          <FormControl>
                            <Input placeholder={t("addresses.cityPlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="district"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("addresses.district")}</FormLabel>
                          <FormControl>
                            <Input placeholder={t("addresses.districtPlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="ward"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("addresses.ward")}</FormLabel>
                          <FormControl>
                            <Input placeholder={t("addresses.wardPlaceholder")} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="postalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("addresses.postalCode")}</FormLabel>
                        <FormControl>
                          <Input placeholder={t("addresses.postalCodePlaceholder")} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="isDefault"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            {t("addresses.setAsDefault")}
                          </FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />
                  
                  <DialogFooter>
                    <Button type="submit">{t("addresses.save")}</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8">
            <p>{t("addresses.loading")}</p>
          </div>
        ) : addresses.length === 0 ? (
          <div className="text-center py-8">
            <p>{t("addresses.noAddresses")}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => setIsAddDialogOpen(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              {t("addresses.addFirst")}
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {addresses.map((address) => (
              <Card key={address._id} className="relative">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">{address.fullName}</div>
                    {address.isDefault && (
                      <Badge variant="outline" className="flex items-center">
                        <Home className="mr-1 h-3 w-3" />
                        {t("addresses.default")}
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="text-sm space-y-1">
                    <p>{address.phoneNumber}</p>
                    <p>{address.addressLine1}</p>
                    {address.addressLine2 && <p>{address.addressLine2}</p>}
                    <p>
                      {address.ward}, {address.district}, {address.city}
                      {address.postalCode && `, ${address.postalCode}`}
                    </p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between pt-2">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditDialog(address)}
                    >
                      <Edit className="mr-1 h-3 w-3" />
                      {t("addresses.edit")}
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Trash className="mr-1 h-3 w-3" />
                          {t("addresses.delete")}
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>{t("addresses.deleteConfirmTitle")}</AlertDialogTitle>
                          <AlertDialogDescription>
                            {t("addresses.deleteConfirmDescription")}
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>{t("addresses.cancel")}</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteAddress(address._id)}
                          >
                            {t("addresses.confirm")}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                  {!address.isDefault && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSetDefaultAddress(address._id)}
                    >
                      <Check className="mr-1 h-3 w-3" />
                      {t("addresses.setDefault")}
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
      
      {/* Dialog chỉnh sửa địa chỉ */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{t("addresses.editTitle")}</DialogTitle>
            <DialogDescription>{t("addresses.editDescription")}</DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleEditAddress)} className="space-y-4">
              {/* Form fields giống như form thêm mới */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("addresses.fullName")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("addresses.fullNamePlaceholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("addresses.phoneNumber")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("addresses.phoneNumberPlaceholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="addressLine1"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("addresses.addressLine1")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("addresses.addressLine1Placeholder")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="addressLine2"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("addresses.addressLine2")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("addresses.addressLine2Placeholder")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("addresses.city")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("addresses.cityPlaceholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="district"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("addresses.district")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("addresses.districtPlaceholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="ward"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("addresses.ward")}</FormLabel>
                      <FormControl>
                        <Input placeholder={t("addresses.wardPlaceholder")} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="postalCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("addresses.postalCode")}</FormLabel>
                    <FormControl>
                      <Input placeholder={t("addresses.postalCodePlaceholder")} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="isDefault"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        {t("addresses.setAsDefault")}
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="submit">{t("addresses.update")}</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
