"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { Button } from "@components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@components/ui/card"
import { toast } from "sonner"
import { Package, ChevronRight, Filter, Search } from "lucide-react"
import { Badge } from "@components/ui/badge"
import { Input } from "@components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@components/ui/pagination"
import { Separator } from "@components/ui/separator"
import { format } from "date-fns"
import { vi } from "date-fns/locale"

interface OrderItem {
  product: {
    _id: string
    name: string
    price: number
    images: string[]
  }
  quantity: number
  price: number
  variant?: string
}

interface ShippingAddress {
  fullName: string
  phoneNumber: string
  addressLine1: string
  city: string
  district: string
  ward: string
}

interface Order {
  _id: string
  orderNumber: string
  items: OrderItem[]
  totalAmount: number
  finalAmount: number
  status: string
  paymentStatus: string
  paymentMethod: string
  shippingAddress: ShippingAddress
  createdAt: string
  updatedAt: string
}

interface OrdersResponse {
  orders: Order[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

export function UserOrders() {
  const { t } = useTranslation("profile")
  const [orders, setOrders] = useState<Order[]>([])
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 5,
    totalPages: 0,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [status, setStatus] = useState<string>("")
  const [searchTerm, setSearchTerm] = useState<string>("")
  
  // Tải danh sách đơn hàng
  const fetchOrders = async (page = 1, status = "", search = "") => {
    setIsLoading(true)
    try {
      let url = `/api/orders/user-history?page=${page}&limit=${pagination.limit}`
      if (status) {
        url += `&status=${status}`
      }
      if (search) {
        url += `&search=${search}`
      }
      
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error("Không thể tải danh sách đơn hàng")
      }
      
      const data: OrdersResponse = await response.json()
      setOrders(data.orders)
      setPagination(data.pagination)
    } catch (error) {
      toast.error("Lỗi khi tải danh sách đơn hàng")
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }
  
  useEffect(() => {
    fetchOrders(pagination.page, status, searchTerm)
  }, [pagination.page, status, searchTerm])
  
  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    if (page < 1 || page > pagination.totalPages) return
    fetchOrders(page, status, searchTerm)
  }
  
  // Xử lý thay đổi trạng thái lọc
  const handleStatusChange = (value: string) => {
    setStatus(value)
    fetchOrders(1, value, searchTerm)
  }
  
  // Xử lý tìm kiếm
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchOrders(1, status, searchTerm)
  }
  
  // Hiển thị trạng thái đơn hàng
  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { label: string, variant: "default" | "outline" | "secondary" | "destructive" | "success" }> = {
      pending: { label: t("orders.statusPending"), variant: "outline" },
      processing: { label: t("orders.statusProcessing"), variant: "secondary" },
      shipped: { label: t("orders.statusShipped"), variant: "default" },
      delivered: { label: t("orders.statusDelivered"), variant: "success" },
      cancelled: { label: t("orders.statusCancelled"), variant: "destructive" },
      returned: { label: t("orders.statusReturned"), variant: "destructive" },
    }
    
    const statusInfo = statusMap[status] || { label: status, variant: "outline" }
    
    return (
      <Badge variant={statusInfo.variant}>
        {statusInfo.label}
      </Badge>
    )
  }
  
  // Hiển thị trạng thái thanh toán
  const getPaymentStatusBadge = (status: string) => {
    const statusMap: Record<string, { label: string, variant: "default" | "outline" | "secondary" | "destructive" | "success" }> = {
      pending: { label: t("orders.paymentPending"), variant: "outline" },
      paid: { label: t("orders.paymentPaid"), variant: "success" },
      failed: { label: t("orders.paymentFailed"), variant: "destructive" },
      refunded: { label: t("orders.paymentRefunded"), variant: "secondary" },
    }
    
    const statusInfo = statusMap[status] || { label: status, variant: "outline" }
    
    return (
      <Badge variant={statusInfo.variant}>
        {statusInfo.label}
      </Badge>
    )
  }
  
  // Format tiền tệ
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(amount)
  }
  
  // Format ngày tháng
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'PPP', { locale: vi })
  }
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Package className="mr-2 h-5 w-5" />
            <div>
              <CardTitle>{t("orders.title")}</CardTitle>
              <CardDescription>{t("orders.description")}</CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <form onSubmit={handleSearch} className="flex items-center space-x-2">
              <Input
                placeholder={t("orders.searchPlaceholder")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-[200px]"
              />
              <Button type="submit" size="sm" variant="outline">
                <Search className="h-4 w-4" />
              </Button>
            </form>
            <Select value={status} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[150px]">
                <div className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder={t("orders.filterByStatus")} />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">{t("orders.allOrders")}</SelectItem>
                <SelectItem value="pending">{t("orders.statusPending")}</SelectItem>
                <SelectItem value="processing">{t("orders.statusProcessing")}</SelectItem>
                <SelectItem value="shipped">{t("orders.statusShipped")}</SelectItem>
                <SelectItem value="delivered">{t("orders.statusDelivered")}</SelectItem>
                <SelectItem value="cancelled">{t("orders.statusCancelled")}</SelectItem>
                <SelectItem value="returned">{t("orders.statusReturned")}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8">
            <p>{t("orders.loading")}</p>
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-8">
            <p>{t("orders.noOrders")}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.href = "/"}
            >
              {t("orders.startShopping")}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => (
              <Card key={order._id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex flex-col md:flex-row md:items-center justify-between">
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium">{t("orders.orderNumber")}: {order.orderNumber}</h3>
                        {getStatusBadge(order.status)}
                        {getPaymentStatusBadge(order.paymentStatus)}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {t("orders.placedOn")}: {formatDate(order.createdAt)}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2 md:mt-0"
                      asChild
                    >
                      <a href={`/orders/${order._id}`}>
                        {t("orders.viewDetails")}
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="space-y-2">
                    {order.items.slice(0, 2).map((item, index) => (
                      <div key={index} className="flex items-center space-x-4">
                        <div className="h-12 w-12 rounded-md overflow-hidden bg-muted">
                          {item.product.images && item.product.images.length > 0 ? (
                            <img
                              src={item.product.images[0]}
                              alt={item.product.name}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="h-full w-full flex items-center justify-center bg-muted">
                              <Package className="h-6 w-6 text-muted-foreground" />
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{item.product.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatCurrency(item.price)} x {item.quantity}
                          </p>
                        </div>
                      </div>
                    ))}
                    {order.items.length > 2 && (
                      <p className="text-sm text-muted-foreground">
                        {t("orders.andMore", { count: order.items.length - 2 })}
                      </p>
                    )}
                  </div>
                </CardContent>
                <Separator />
                <CardFooter className="flex justify-between py-3">
                  <div className="text-sm">
                    <span className="text-muted-foreground">{t("orders.total")}:</span>{" "}
                    <span className="font-medium">{formatCurrency(order.finalAmount)}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-muted-foreground">{t("orders.paymentMethod")}:</span>{" "}
                    <span className="font-medium capitalize">{order.paymentMethod}</span>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
        
        {!isLoading && orders.length > 0 && pagination.totalPages > 1 && (
          <Pagination className="mt-6">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => handlePageChange(pagination.page - 1)}
                  className={pagination.page <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
              
              {Array.from({ length: pagination.totalPages }).map((_, i) => {
                const page = i + 1;
                
                // Hiển thị trang đầu, trang cuối và các trang xung quanh trang hiện tại
                if (
                  page === 1 ||
                  page === pagination.totalPages ||
                  (page >= pagination.page - 1 && page <= pagination.page + 1)
                ) {
                  return (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={page === pagination.page}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  );
                }
                
                // Hiển thị dấu ... nếu có khoảng cách
                if (
                  (page === 2 && pagination.page > 3) ||
                  (page === pagination.totalPages - 1 && pagination.page < pagination.totalPages - 2)
                ) {
                  return (
                    <PaginationItem key={page}>
                      <PaginationEllipsis />
                    </PaginationItem>
                  );
                }
                
                return null;
              })}
              
              <PaginationItem>
                <PaginationNext
                  onClick={() => handlePageChange(pagination.page + 1)}
                  className={pagination.page >= pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </CardContent>
    </Card>
  )
}
