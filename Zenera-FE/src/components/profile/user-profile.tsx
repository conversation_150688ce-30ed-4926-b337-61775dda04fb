"use client"

import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useProfile, useUpdateProfile } from "@hooks/use-auth"
import { Button } from "@components/ui/button"
import { Input } from "@components/ui/input"
import { Textarea } from "@components/ui/textarea"
import { Label } from "@components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@components/ui/avatar"
import { toast } from "sonner"
import { Pencil, User, Mail, Phone, Save } from "lucide-react"

export function UserProfile() {
  const { t } = useTranslation("profile")
  const { data: user } = useProfile()
  const updateProfile = useUpdateProfile()
  
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: user?.name || "",
    avatar: user?.avatar || "",
    phone: user?.phone || "",
    bio: user?.bio || "",
  })
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      await updateProfile.mutateAsync(formData)
      toast.success(t("profile.updateSuccess"))
      setIsEditing(false)
    } catch (error) {
      toast.error(t("profile.updateError"))
    }
  }
  
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2)
  }
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("profile.title")}</CardTitle>
            <CardDescription>{t("profile.description")}</CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(!isEditing)}
          >
            {isEditing ? t("profile.cancel") : (
              <>
                <Pencil className="mr-2 h-4 w-4" />
                {t("profile.edit")}
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex flex-col items-center space-y-2">
              <Avatar className="h-32 w-32">
                <AvatarImage src={formData.avatar} alt={user?.name} />
                <AvatarFallback>{user?.name ? getInitials(user.name) : "U"}</AvatarFallback>
              </Avatar>
              {isEditing && (
                <div className="w-full">
                  <Label htmlFor="avatar" className="text-xs text-muted-foreground">
                    {t("profile.avatarUrl")}
                  </Label>
                  <Input
                    id="avatar"
                    name="avatar"
                    value={formData.avatar}
                    onChange={handleChange}
                    placeholder={t("profile.avatarUrlPlaceholder")}
                    className="mt-1"
                  />
                </div>
              )}
            </div>
            
            <div className="space-y-4 flex-1">
              <div>
                <div className="flex items-center">
                  <User className="mr-2 h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="name">{t("profile.name")}</Label>
                </div>
                {isEditing ? (
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder={t("profile.namePlaceholder")}
                    className="mt-1"
                  />
                ) : (
                  <p className="text-lg font-medium mt-1">{user?.name}</p>
                )}
              </div>
              
              <div>
                <div className="flex items-center">
                  <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="email">{t("profile.email")}</Label>
                </div>
                <p className="text-muted-foreground mt-1">{user?.email}</p>
              </div>
              
              <div>
                <div className="flex items-center">
                  <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="phone">{t("profile.phone")}</Label>
                </div>
                {isEditing ? (
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder={t("profile.phonePlaceholder")}
                    className="mt-1"
                  />
                ) : (
                  <p className="text-muted-foreground mt-1">
                    {user?.phone || t("profile.noPhone")}
                  </p>
                )}
              </div>
              
              <div>
                <Label htmlFor="bio">{t("profile.bio")}</Label>
                {isEditing ? (
                  <Textarea
                    id="bio"
                    name="bio"
                    value={formData.bio}
                    onChange={handleChange}
                    placeholder={t("profile.bioPlaceholder")}
                    className="mt-1"
                    rows={4}
                  />
                ) : (
                  <p className="text-muted-foreground mt-1 whitespace-pre-line">
                    {user?.bio || t("profile.noBio")}
                  </p>
                )}
              </div>
            </div>
          </div>
          
          {isEditing && (
            <div className="mt-6 flex justify-end">
              <Button type="submit" disabled={updateProfile.isPending}>
                <Save className="mr-2 h-4 w-4" />
                {updateProfile.isPending ? t("profile.saving") : t("profile.save")}
              </Button>
            </div>
          )}
        </form>
      </CardContent>
      <CardFooter className="border-t px-6 py-4">
        <div className="flex items-center justify-between w-full text-sm">
          <div className="text-muted-foreground">
            {t("profile.memberSince", { date: new Date(user?.createdAt || Date.now()).toLocaleDateString() })}
          </div>
          <div className="flex items-center">
            <div className="text-muted-foreground mr-2">{t("profile.role")}:</div>
            <div className="font-medium capitalize">{user?.role}</div>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
