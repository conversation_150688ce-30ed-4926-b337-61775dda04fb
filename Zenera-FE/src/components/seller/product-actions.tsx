"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useTranslation } from "react-i18next"
import { 
  MoreHorizontal, Edit, Copy, Trash2, 
  AlertTriangle, ExternalLink, Eye 
} from "lucide-react"
import { Button } from "@components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@components/ui/alert-dialog"
import { useToast } from "@components/ui/use-toast"

interface ProductActionsProps {
  productId: string
  productName: string
}

export default function ProductActions({ productId, productName }: ProductActionsProps) {
  const { t } = useTranslation("seller")
  const router = useRouter()
  const { toast } = useToast()
  
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isDuplicating, setIsDuplicating] = useState(false)

  const handleEdit = () => {
    router.push(`/seller/products/edit/${productId}`)
  }

  const handleView = () => {
    // Open in a new tab
    window.open(`/products/${productId}`, "_blank")
  }

  const handleDuplicate = async () => {
    try {
      setIsDuplicating(true)
      
      const response = await fetch(`/api/seller/products/${productId}/duplicate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to duplicate product")
      }
      
      const duplicatedProduct = await response.json()
      
      toast({
        title: t("productDuplicated") || "Product duplicated",
        description: t("productDuplicatedDesc") || "Product has been duplicated successfully.",
      })
      
      // Redirect to the edit page of the duplicated product
      router.push(`/seller/products/edit/${duplicatedProduct._id}`)
      
    } catch (error) {
      console.error("Error duplicating product:", error)
      toast({
        title: t("error") || "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsDuplicating(false)
    }
  }

  const handleDelete = async () => {
    try {
      setIsDeleting(true)
      
      const response = await fetch(`/api/seller/products/${productId}`, {
        method: "DELETE",
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to delete product")
      }
      
      toast({
        title: t("productDeleted") || "Product deleted",
        description: t("productDeletedDesc") || "Product has been deleted successfully.",
      })
      
      // Refresh the product list
      router.refresh()
      
    } catch (error) {
      console.error("Error deleting product:", error)
      toast({
        title: t("error") || "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
      setIsDeleteDialogOpen(false)
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">{t("actions") || "Actions"}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>{t("actions") || "Actions"}</DropdownMenuLabel>
          <DropdownMenuItem onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            <span>{t("edit") || "Edit"}</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleView}>
            <Eye className="mr-2 h-4 w-4" />
            <span>{t("view") || "View"}</span>
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={handleDuplicate}
            disabled={isDuplicating}
          >
            <Copy className="mr-2 h-4 w-4" />
            <span>
              {isDuplicating 
                ? (t("duplicating") || "Duplicating...") 
                : (t("duplicate") || "Duplicate")}
            </span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={() => setIsDeleteDialogOpen(true)}
            className="text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            <span>{t("delete") || "Delete"}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("deleteProduct") || "Delete Product"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("deleteProductConfirmation", { productName }) || 
                `Are you sure you want to delete "${productName}"? This action cannot be undone.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              {t("cancel") || "Cancel"}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting 
                ? (t("deleting") || "Deleting...") 
                : (t("delete") || "Delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
