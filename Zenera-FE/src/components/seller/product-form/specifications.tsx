"use client"

import { useState } from "react"
import { But<PERSON> } from "@components/ui/button"
import { Card, CardContent } from "@components/ui/card"
import { Input } from "@components/ui/input"
import { Label } from "@components/ui/label"
import { Plus, Trash2, ArrowLeft, Save } from "lucide-react"
import { useTranslation } from "react-i18next"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@components/ui/table"
import { Badge } from "@components/ui/badge"

interface SpecificationsFormProps {
  initialSpecifications?: Record<string, string>
  onSave: (specifications: Record<string, string>) => void
  onPrevious?: () => void
}

export default function SpecificationsForm({ 
  initialSpecifications = {}, 
  onSave, 
  onPrevious 
}: SpecificationsFormProps) {
  const { t } = useTranslation("seller")
  const [specifications, setSpecifications] = useState<Record<string, string>>(initialSpecifications)
  const [new<PERSON><PERSON>, setN<PERSON><PERSON><PERSON>] = useState("")
  const [newValue, setNewValue] = useState("")
  const [error, setError] = useState("")

  // Common specification templates for different product types
  const specTemplates = {
    electronics: {
      "Brand": "",
      "Model": "",
      "Dimensions": "",
      "Weight": "",
      "Color": "",
      "Warranty": "",
      "Power": "",
      "Connectivity": "",
    },
    clothing: {
      "Brand": "",
      "Material": "",
      "Size": "",
      "Color": "",
      "Care Instructions": "",
      "Style": "",
      "Fit": "",
    },
    furniture: {
      "Brand": "",
      "Material": "",
      "Dimensions": "",
      "Weight": "",
      "Color": "",
      "Assembly Required": "",
      "Warranty": "",
    },
    books: {
      "Author": "",
      "Publisher": "",
      "Publication Date": "",
      "Language": "",
      "ISBN": "",
      "Pages": "",
      "Format": "",
    }
  }

  const handleAddSpecification = () => {
    if (!newKey.trim()) {
      setError("Specification name cannot be empty")
      return
    }

    if (specifications[newKey]) {
      setError("Specification name already exists")
      return
    }

    setSpecifications(prev => ({
      ...prev,
      [newKey]: newValue
    }))
    setNewKey("")
    setNewValue("")
    setError("")
  }

  const handleRemoveSpecification = (key: string) => {
    setSpecifications(prev => {
      const newSpecs = { ...prev }
      delete newSpecs[key]
      return newSpecs
    })
  }

  const handleApplyTemplate = (template: keyof typeof specTemplates) => {
    // Merge existing specifications with template
    setSpecifications(prev => ({
      ...specTemplates[template],
      ...prev
    }))
  }

  const handleSave = () => {
    onSave(specifications)
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">
              {t("productSpecifications") || "Product Specifications"}
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              {t("specificationsDescription") || "Add technical specifications for your product to help customers make informed decisions."}
            </p>

            <div className="flex flex-wrap gap-2 mb-4">
              <Label className="w-full text-sm font-medium mb-1">
                {t("quickTemplates") || "Quick Templates"}
              </Label>
              {Object.keys(specTemplates).map(template => (
                <Button 
                  key={template} 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleApplyTemplate(template as keyof typeof specTemplates)}
                >
                  {template.charAt(0).toUpperCase() + template.slice(1)}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="specKey">{t("specificationName") || "Specification Name"}</Label>
              <Input
                id="specKey"
                value={newKey}
                onChange={(e) => setNewKey(e.target.value)}
                placeholder={t("enterSpecName") || "e.g., Weight, Dimensions, Material"}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="specValue">{t("specificationValue") || "Specification Value"}</Label>
              <div className="flex gap-2">
                <Input
                  id="specValue"
                  value={newValue}
                  onChange={(e) => setNewValue(e.target.value)}
                  placeholder={t("enterSpecValue") || "e.g., 500g, 10x15x2 cm, Cotton"}
                  className="flex-1"
                />
                <Button 
                  type="button" 
                  onClick={handleAddSpecification}
                  disabled={!newKey.trim()}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">
              {t("currentSpecifications") || "Current Specifications"}
            </h4>
            
            {Object.keys(specifications).length > 0 ? (
              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-1/3">{t("specification") || "Specification"}</TableHead>
                      <TableHead>{t("value") || "Value"}</TableHead>
                      <TableHead className="w-16"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(specifications).map(([key, value]) => (
                      <TableRow key={key}>
                        <TableCell className="font-medium">{key}</TableCell>
                        <TableCell>{value || <span className="text-gray-400 italic">Not specified</span>}</TableCell>
                        <TableCell>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            onClick={() => handleRemoveSpecification(key)}
                          >
                            <Trash2 className="w-4 h-4 text-red-500" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 border border-dashed rounded-md">
                <p className="text-sm text-gray-500">
                  {t("noSpecificationsAdded") || "No specifications added yet"}
                </p>
              </div>
            )}
          </div>

          <div className="flex justify-between">
            {onPrevious && (
              <Button type="button" variant="outline" onClick={onPrevious}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                {t("previous") || "Previous"}
              </Button>
            )}
            <Button type="button" onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              {t("saveSpecifications") || "Save Specifications"}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
