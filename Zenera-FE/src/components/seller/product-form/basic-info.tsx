"use client"

import { useState, useEffect } from "react"
import { Input } from "@components/ui/input"
import { Button } from "@components/ui/button"
import { Label } from "@components/ui/label"
import { Textarea } from "@components/ui/textarea"
import { Switch } from "@components/ui/switch"
import { Card, CardContent } from "@components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { useTranslation } from "react-i18next"

// Updated category structure
const staticCategories = [
  {
    id: "uncategorized",
    name: "Uncategorized",
    subcategories: [],
  },
  {
    id: "electronics",
    name: "Electronics",
    subcategories: [
      {
        id: "computers",
        name: "Computers",
        subsubcategories: [
          { id: "laptops", name: "Laptops" },
          { id: "desktops", name: "Desktops" },
          { id: "tablets", name: "Tablets" },
        ],
      },
      {
        id: "phones",
        name: "Phones & Accessories",
        subsubcategories: [
          { id: "smartphones", name: "Smartphones" },
          { id: "cases", name: "Cases" },
          { id: "chargers", name: "Chargers" },
        ],
      },
    ],
  },
  {
    id: "clothing",
    name: "Clothing",
    subcategories: [
      {
        id: "mens",
        name: "Men's Clothing",
        subsubcategories: [
          { id: "shirts", name: "Shirts" },
          { id: "pants", name: "Pants" },
          { id: "shoes", name: "Shoes" },
        ],
      },
      {
        id: "womens",
        name: "Women's Clothing",
        subsubcategories: [
          { id: "dresses", name: "Dresses" },
          { id: "tops", name: "Tops" },
          { id: "shoes", name: "Shoes" },
        ],
      },
    ],
  },
]

export interface BasicProductInfo {
  title: string
  description: string
  price: number
  discountPrice?: number
  stock: number
  minStockThreshold?: number
  category: string
  featured: boolean
}

interface BasicInfoFormProps {
  initialData?: Partial<BasicProductInfo>
  onSave: (data: BasicProductInfo) => void
  onNext?: () => void
}

export default function BasicInfoForm({ initialData, onSave, onNext }: BasicInfoFormProps) {
  const { t } = useTranslation("seller")
  
  const [formData, setFormData] = useState<BasicProductInfo>({
    title: initialData?.title || "",
    description: initialData?.description || "",
    price: initialData?.price || 0,
    discountPrice: initialData?.discountPrice || 0,
    stock: initialData?.stock || 0,
    minStockThreshold: initialData?.minStockThreshold || 5,
    category: initialData?.category || "",
    featured: initialData?.featured || false,
  })

  const [selectedCategory, setSelectedCategory] = useState(initialData?.category || "")
  const [selectedSubcategory, setSelectedSubcategory] = useState("")
  const [selectedSubsubcategory, setSelectedSubsubcategory] = useState("")

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ 
      ...prev, 
      [name]: name === "price" || name === "discountPrice" || name === "stock" || name === "minStockThreshold" 
        ? parseFloat(value) 
        : value 
    }))
  }

  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, featured: checked }))
  }

  const handleCategoryConfirm = () => {
    // Store the most specific category selected
    let categoryId = selectedCategory
    if (selectedSubsubcategory) {
      categoryId = selectedSubsubcategory
    } else if (selectedSubcategory) {
      categoryId = selectedSubcategory
    }

    setFormData((prev) => ({
      ...prev,
      category: categoryId,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
    if (onNext) onNext()
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">{t("productName") || "Product Name"}</Label>
            <Input 
              id="title" 
              name="title" 
              value={formData.title} 
              onChange={handleChange} 
              placeholder={t("enterProductName") || "Enter product name"} 
              required 
            />
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>{t("category") || "Category"}</Label>
              <Select 
                value={selectedCategory} 
                onValueChange={(value) => {
                  setSelectedCategory(value)
                  setSelectedSubcategory("")
                  setSelectedSubsubcategory("")
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t("selectCategory") || "Select Category"} />
                </SelectTrigger>
                <SelectContent>
                  {staticCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedCategory && staticCategories.find(c => c.id === selectedCategory)?.subcategories.length > 0 && (
              <div className="space-y-2">
                <Label>{t("subcategory") || "Subcategory"}</Label>
                <Select 
                  value={selectedSubcategory} 
                  onValueChange={(value) => {
                    setSelectedSubcategory(value)
                    setSelectedSubsubcategory("")
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("selectSubcategory") || "Select Subcategory"} />
                  </SelectTrigger>
                  <SelectContent>
                    {staticCategories
                      .find((c) => c.id === selectedCategory)
                      ?.subcategories.map((subcategory) => (
                        <SelectItem key={subcategory.id} value={subcategory.id}>
                          {subcategory.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {selectedSubcategory && staticCategories
              .find(c => c.id === selectedCategory)
              ?.subcategories.find(sc => sc.id === selectedSubcategory)
              ?.subsubcategories.length > 0 && (
              <div className="space-y-2">
                <Label>{t("subSubcategory") || "Sub-subcategory"}</Label>
                <Select 
                  value={selectedSubsubcategory} 
                  onValueChange={setSelectedSubsubcategory}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("selectSubSubcategory") || "Select Sub-subcategory"} />
                  </SelectTrigger>
                  <SelectContent>
                    {staticCategories
                      .find((c) => c.id === selectedCategory)
                      ?.subcategories.find((sc) => sc.id === selectedSubcategory)
                      ?.subsubcategories.map((subsubcategory) => (
                        <SelectItem key={subsubcategory.id} value={subsubcategory.id}>
                          {subsubcategory.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {(selectedCategory || selectedSubcategory || selectedSubsubcategory) && (
              <Button
                type="button"
                onClick={handleCategoryConfirm}
                variant="outline"
                className="mt-2"
              >
                {t("confirmCategorySelection") || "Confirm Category Selection"}
              </Button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="price">{t("price") || "Price"}</Label>
              <Input
                id="price"
                name="price"
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={handleChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="discountPrice">{t("discountPrice") || "Discount Price"}</Label>
              <Input
                id="discountPrice"
                name="discountPrice"
                type="number"
                step="0.01"
                min="0"
                value={formData.discountPrice}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stock">{t("stock") || "Stock"}</Label>
              <Input
                id="stock"
                name="stock"
                type="number"
                min="0"
                value={formData.stock}
                onChange={handleChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="minStockThreshold">{t("minStockThreshold") || "Min Stock Threshold"}</Label>
              <Input
                id="minStockThreshold"
                name="minStockThreshold"
                type="number"
                min="0"
                value={formData.minStockThreshold}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch 
              id="featured" 
              checked={formData.featured} 
              onCheckedChange={handleSwitchChange} 
            />
            <Label htmlFor="featured">{t("featured") || "Featured Product"}</Label>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">{t("description") || "Description"}</Label>
            <Textarea 
              id="description" 
              name="description" 
              value={formData.description} 
              onChange={handleChange} 
              rows={5}
              required 
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="submit">
              {onNext 
                ? (t("saveAndContinue") || "Save & Continue") 
                : (initialData ? (t("updateProduct") || "Update Product") : (t("addProduct") || "Add Product"))}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
