"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@components/ui/card"
import { Button } from "@components/ui/button"
import { useToast } from "@components/ui/use-toast"
import { useTranslation } from "react-i18next"
import { Package, Image as ImageIcon, FileText, Barcode } from "lucide-react"
import BasicInfoForm, { BasicProductInfo } from "./basic-info"
import ImagesManager from "./images-manager"
import SpecificationsForm from "./specifications"
import BarcodeScannerCamera from "@components/bar-code/scanner-camera"
import BarcodeScannerImage from "@components/bar-code/scanner-image"
import { BarcodeData } from "../product-form"

interface ProductFormProps {
  initialData?: any
  onSuccess?: (data: any) => void
}

export default function ProductForm({ initialData, onSuccess }: ProductFormProps) {
  const { t } = useTranslation("seller")
  const { toast } = useToast()
  
  const [activeTab, setActiveTab] = useState("basic-info")
  const [activeQrTab, setActiveQrTab] = useState("camera")
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const [productData, setProductData] = useState({
    title: initialData?.title || "",
    description: initialData?.description || "",
    price: initialData?.price || 0,
    discountPrice: initialData?.discountPrice || 0,
    stock: initialData?.stock || 0,
    minStockThreshold: initialData?.minStockThreshold || 5,
    category: initialData?.category?._id || "",
    featured: initialData?.featured || false,
    images: initialData?.images || [],
    specifications: initialData?.specifications || {},
  })

  const handleBasicInfoSave = (data: BasicProductInfo) => {
    setProductData(prev => ({
      ...prev,
      title: data.title,
      description: data.description,
      price: data.price,
      discountPrice: data.discountPrice,
      stock: data.stock,
      minStockThreshold: data.minStockThreshold,
      category: data.category,
      featured: data.featured,
    }))
    
    toast({
      title: t("basicInfoSaved") || "Basic information saved",
      description: t("basicInfoSavedDesc") || "Product basic information has been saved.",
    })
    
    setActiveTab("images")
  }

  const handleImagesSave = (images: string[]) => {
    setProductData(prev => ({
      ...prev,
      images,
    }))
    
    toast({
      title: t("imagesSaved") || "Images saved",
      description: t("imagesSavedDesc") || "Product images have been saved.",
    })
    
    setActiveTab("specifications")
  }

  const handleSpecificationsSave = (specifications: Record<string, string>) => {
    setProductData(prev => ({
      ...prev,
      specifications,
    }))
    
    toast({
      title: t("specificationsSaved") || "Specifications saved",
      description: t("specificationsSavedDesc") || "Product specifications have been saved.",
    })
    
    handleSubmit()
  }

  const handleQRScanComplete = (data: BarcodeData) => {
    setProductData(prev => ({
      ...prev,
      title: data.productName || prev.title,
      price: parseFloat(data.unitPrice) || prev.price,
      description: data.notes || prev.description,
    }))

    // Switch to basic info tab to show the populated form
    setActiveTab("basic-info")
    
    toast({
      title: t("qrScanned") || "QR Code scanned",
      description: t("qrScannedDesc") || "Product information has been populated from QR code.",
    })
  }

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true)
      
      // Convert specifications from object to array of key-value pairs for API
      const specArray = Object.entries(productData.specifications).map(([key, value]) => ({
        key,
        value
      }))
      
      const apiData = {
        title: productData.title,
        description: productData.description,
        price: productData.price,
        discountPrice: productData.discountPrice || undefined,
        stock: productData.stock,
        minStockThreshold: productData.minStockThreshold,
        category: productData.category,
        featured: productData.featured,
        specifications: productData.specifications,
      }
      
      console.log("Submitting product data:", apiData)
      
      // If we have initialData, this is an edit operation
      if (initialData?._id) {
        const response = await fetch(`/api/seller/products/${initialData._id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to update product")
        }

        const updatedProduct = await response.json()
        
        // Update images if changed
        if (productData.images.length > 0) {
          await fetch(`/api/seller/products/${initialData._id}/images`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ images: productData.images }),
          })
        }
        
        toast({
          title: t("productUpdated") || "Product updated",
          description: t("productUpdatedDesc") || "Product has been updated successfully.",
        })
        
        if (onSuccess) onSuccess(updatedProduct)
      } else {
        // Create new product
        const response = await fetch('/api/seller/products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to create product")
        }

        const newProduct = await response.json()
        
        // Update images if any
        if (productData.images.length > 0) {
          await fetch(`/api/seller/products/${newProduct._id}/images`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ images: productData.images }),
          })
        }
        
        toast({
          title: t("productCreated") || "Product created",
          description: t("productCreatedDesc") || "Product has been created successfully.",
        })
        
        if (onSuccess) onSuccess(newProduct)
      }
    } catch (error) {
      console.error("Error submitting product:", error)
      toast({
        title: t("error") || "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="container max-w-4xl py-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic-info" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            <span className="hidden sm:inline">{t("basicInfo") || "Basic Info"}</span>
          </TabsTrigger>
          <TabsTrigger value="images" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            <span className="hidden sm:inline">{t("images") || "Images"}</span>
          </TabsTrigger>
          <TabsTrigger value="specifications" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">{t("specifications") || "Specifications"}</span>
          </TabsTrigger>
          <TabsTrigger value="qr-scan" className="flex items-center gap-2">
            <Barcode className="h-4 w-4" />
            <span className="hidden sm:inline">{t("qrScan") || "QR Scan"}</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic-info" className="space-y-4">
          <BasicInfoForm 
            initialData={productData} 
            onSave={handleBasicInfoSave} 
            onNext={() => setActiveTab("images")}
          />
        </TabsContent>

        <TabsContent value="images" className="space-y-4">
          <ImagesManager 
            initialImages={productData.images}
            onSave={handleImagesSave}
            onNext={() => setActiveTab("specifications")}
            onPrevious={() => setActiveTab("basic-info")}
            productId={initialData?._id}
          />
        </TabsContent>

        <TabsContent value="specifications" className="space-y-4">
          <SpecificationsForm 
            initialSpecifications={productData.specifications}
            onSave={handleSpecificationsSave}
            onPrevious={() => setActiveTab("images")}
          />
        </TabsContent>

        <TabsContent value="qr-scan" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t("scanProductQrCode") || "Scan Product QR Code"}</CardTitle>
              <CardDescription>
                {t("scanQrCodeDesc") || "Scan a product QR code to automatically fill in product details"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="camera" value={activeQrTab} onValueChange={setActiveQrTab}>
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="camera">{t("camera") || "Camera"}</TabsTrigger>
                  <TabsTrigger value="image">{t("uploadImage") || "Upload Image"}</TabsTrigger>
                </TabsList>
                <TabsContent value="camera" className="mt-4">
                  <BarcodeScannerCamera onScanComplete={handleQRScanComplete} />
                </TabsContent>
                <TabsContent value="image" className="mt-4">
                  <BarcodeScannerImage onScanComplete={handleQRScanComplete} />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
