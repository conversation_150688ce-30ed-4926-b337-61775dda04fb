"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { useAnalytics } from "@hooks/use-analytics"
import { useAnalyticsState } from "@store/seller/analytics/analytics.state"
import { Button } from "@components/ui/button"
import { Skeleton } from "@components/ui/skeleton"
import { Package } from "lucide-react"
import KpiMetrics from "@components/seller/dashboard/kpi-metrics"
import OrderOverview from "@components/seller/dashboard/order-overview"
import RevenueChartEnhanced from "@components/seller/dashboard/revenue-chart-enhanced"
import LowStockAlertWidget from "@components/seller/dashboard/low-stock-alert-widget"

export default function Dashboard() {
  const { t } = useTranslation("seller-dashboard")
  const [timeFrame, setTimeFrame] = useState<"day" | "3days" | "week" | "month" | "year">("week")
  const [comparison, setComparison] = useState<"previous" | "year_ago" | "none">("previous")

  // Sử dụng hook useAnalytics với các tham số mới
  const { data, isLoading, error } = useAnalytics({
    timeFrame,
    comparison
  })
  const analyticsState = useAnalyticsState()

  // Handle API errors
  const [apiError, setApiError] = useState<string | null>(null)

  useEffect(() => {
    if (error) {
      console.error('Analytics API error:', error)
      setApiError(error instanceof Error ? error.message : 'Failed to load analytics data')
    } else {
      setApiError(null)
    }
  }, [error])

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value)
  }

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  // Function to seed analytics data
  const seedAnalyticsData = async () => {
    try {
      const response = await fetch('/api/seed/analytics')
      const data = await response.json()
      if (response.ok) {
        alert(`Analytics data seeded successfully! Created ${data.ordersCount} orders.`)
        window.location.reload()
      } else {
        alert(`Error: ${data.error || 'Failed to seed data'}`)
      }
    } catch (error) {
      console.error('Error seeding analytics data:', error)
      alert('Failed to seed analytics data. See console for details.')
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Skeleton for KPI Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          {Array(5).fill(0).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>

        {/* Skeleton for Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <Skeleton className="h-[300px] lg:col-span-3" />
          <Skeleton className="h-[300px]" />
        </div>

        {/* Skeleton for Order Overview */}
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }

  // Show API error if any
  if (apiError) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="mb-4 text-red-500">
          <Package className="h-16 w-16 mx-auto mb-2" />
          <h2 className="text-2xl font-bold">Error Loading Analytics</h2>
        </div>
        <p className="mb-6 text-gray-600 max-w-md">
          {apiError}
        </p>
        <Button onClick={() => window.location.reload()}>Retry</Button>
      </div>
    )
  }

  // Check if we have any data
  const hasData = !isLoading && data && data.dailySales && data.dailySales.length > 0

  if (!isLoading && !hasData) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="mb-4 text-amber-500">
          <Package className="h-16 w-16 mx-auto mb-2" />
          <h2 className="text-2xl font-bold">{t("noAnalyticsDataAvailable")}</h2>
        </div>
        <p className="mb-6 text-gray-600 max-w-md">
          {t("noAnalyticsDataMessage")}
        </p>
        <Button onClick={seedAnalyticsData}>{t("seedAnalyticsData")}</Button>
      </div>
    )
  }

  // Xử lý khi thay đổi timeFrame
  const handleTimeFrameChange = (newTimeFrame: "day" | "3days" | "week" | "month" | "year") => {
    setTimeFrame(newTimeFrame)
  }

  // Xử lý khi thay đổi comparison
  const handleComparisonChange = (newComparison: "previous" | "year_ago" | "none") => {
    setComparison(newComparison)
  }

  return (
    <div className="space-y-6">
      {/* KPI Metrics */}
      <KpiMetrics />

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Revenue Chart */}
        <RevenueChartEnhanced
          onTimeFrameChange={handleTimeFrameChange}
          onComparisonChange={handleComparisonChange}
        />

        {/* Low Stock Alert */}
        <LowStockAlertWidget />
      </div>

      {/* Order Overview */}
      <OrderOverview />
    </div>
  )
}
