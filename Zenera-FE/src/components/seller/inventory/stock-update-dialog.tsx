"use client"

import { useState } from "react"
import { useTranslation } from "react-i18next"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@components/ui/dialog"
import { Button } from "@components/ui/button"
import { Input } from "@components/ui/input"
import { Label } from "@components/ui/label"
import { Textarea } from "@components/ui/textarea"
import { useToast } from "@components/ui/use-toast"
import { Plus, Minus, AlertTriangle } from "lucide-react"

interface Product {
  _id: string
  title: string
  stock: number
}

interface StockUpdateDialogProps {
  product: Product
  updateType: "add" | "remove" | "set"
  open: boolean
  onOpenChange: (open: boolean) => void
  onStockUpdated?: () => void
}

export function StockUpdateDialog({
  product,
  updateType,
  open,
  onOpenChange,
  onStockUpdated,
}: StockUpdateDialogProps) {
  const { t } = useTranslation("seller")
  const { toast } = useToast()
  
  const [quantity, setQuantity] = useState<number>(updateType === "set" ? product.stock : 1)
  const [notes, setNotes] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value)
    if (!isNaN(value) && value >= 0) {
      setQuantity(value)
      setError(null)
    }
  }

  const handleSubmit = async () => {
    // Validate quantity
    if (quantity <= 0) {
      setError(t("quantityMustBePositive") || "Quantity must be a positive number")
      return
    }

    if (updateType === "remove" && quantity > product.stock) {
      setError(
        t("cannotRemoveMoreThanAvailable") || 
        "Cannot remove more than the available stock"
      )
      return
    }

    try {
      setIsSubmitting(true)
      setError(null)

      // Prepare data for API call
      const data = {
        items: [
          {
            productId: product._id,
            quantity: quantity,
            actionType: updateType === "set" ? "adjust" : updateType,
            notes: notes || undefined,
          },
        ],
      }

      // Call the batch update API
      const response = await fetch("/api/seller/inventory/batch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update stock")
      }

      // Show success message
      toast({
        title: t("stockUpdated") || "Stock Updated",
        description: getSuccessMessage(),
      })

      // Close dialog and notify parent
      onOpenChange(false)
      if (onStockUpdated) {
        onStockUpdated()
      }
    } catch (error) {
      console.error("Error updating stock:", error)
      setError(error instanceof Error ? error.message : "An unknown error occurred")
      
      toast({
        title: t("error") || "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getSuccessMessage = () => {
    switch (updateType) {
      case "add":
        return t("stockAddedSuccess", { quantity, product: product.title }) || 
          `Added ${quantity} units to ${product.title}`
      case "remove":
        return t("stockRemovedSuccess", { quantity, product: product.title }) || 
          `Removed ${quantity} units from ${product.title}`
      case "set":
        return t("stockSetSuccess", { quantity, product: product.title }) || 
          `Set ${product.title} stock to ${quantity} units`
      default:
        return t("stockUpdatedSuccess") || "Stock updated successfully"
    }
  }

  const getDialogTitle = () => {
    switch (updateType) {
      case "add":
        return t("addStock") || "Add Stock"
      case "remove":
        return t("removeStock") || "Remove Stock"
      case "set":
        return t("setStock") || "Set Stock"
      default:
        return t("updateStock") || "Update Stock"
    }
  }

  const getDialogDescription = () => {
    switch (updateType) {
      case "add":
        return t("addStockDesc", { product: product.title }) || 
          `Add stock units to ${product.title}`
      case "remove":
        return t("removeStockDesc", { product: product.title }) || 
          `Remove stock units from ${product.title}`
      case "set":
        return t("setStockDesc", { product: product.title }) || 
          `Set the exact stock level for ${product.title}`
      default:
        return t("updateStockDesc") || "Update product stock level"
    }
  }

  const getActionIcon = () => {
    switch (updateType) {
      case "add":
        return <Plus className="h-4 w-4 mr-2" />
      case "remove":
        return <Minus className="h-4 w-4 mr-2" />
      default:
        return null
    }
  }

  const getActionButtonText = () => {
    switch (updateType) {
      case "add":
        return t("addStock") || "Add Stock"
      case "remove":
        return t("removeStock") || "Remove Stock"
      case "set":
        return t("setStock") || "Set Stock"
      default:
        return t("update") || "Update"
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
          <DialogDescription>{getDialogDescription()}</DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="current-stock">
              {t("currentStock") || "Current Stock"}
            </Label>
            <Input
              id="current-stock"
              value={product.stock}
              disabled
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="quantity">
              {updateType === "add" 
                ? (t("quantityToAdd") || "Quantity to Add") 
                : updateType === "remove" 
                  ? (t("quantityToRemove") || "Quantity to Remove")
                  : (t("newStockLevel") || "New Stock Level")}
            </Label>
            <Input
              id="quantity"
              type="number"
              min={0}
              value={quantity}
              onChange={handleQuantityChange}
            />
            {error && (
              <p className="text-sm text-red-500 mt-1">
                <AlertTriangle className="h-3 w-3 inline-block mr-1" />
                {error}
              </p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="notes">
              {t("notes") || "Notes"} <span className="text-gray-400 text-xs">({t("optional") || "Optional"})</span>
            </Label>
            <Textarea
              id="notes"
              placeholder={t("notesPlaceholder") || "Enter notes about this stock update"}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            {t("cancel") || "Cancel"}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>{t("updating") || "Updating..."}</>
            ) : (
              <>
                {getActionIcon()}
                {getActionButtonText()}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
