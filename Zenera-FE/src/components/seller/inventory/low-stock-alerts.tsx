"use client"

import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@components/ui/card"
import { Input } from "@components/ui/input"
import { Button } from "@components/ui/button"
import { Badge } from "@components/ui/badge"
import { Skeleton } from "@components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select"
import { Search, AlertTriangle, Plus, Settings } from "lucide-react"
import Image from "next/image"
import { ThresholdDialog } from "./threshold-dialog"

interface Product {
  _id: string
  title: string
  price: number
  stock: number
  minStockThreshold?: number
  category: {
    _id: string
    name: string
  }
  images: string[]
}

export default function LowStockAlerts() {
  const { t } = useTranslation("seller")
  const router = useRouter()
  
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState<string>("stock_low")
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [isThresholdDialogOpen, setIsThresholdDialogOpen] = useState(false)

  useEffect(() => {
    const fetchLowStockProducts = async () => {
      try {
        setLoading(true)
        
        // Build query parameters
        const params = new URLSearchParams()
        params.append("stockFilter", "low_stock")
        
        if (searchQuery) {
          params.append("query", searchQuery)
        }
        
        if (sortBy) {
          params.append("sort", sortBy)
        }
        
        const response = await fetch(`/api/seller/products?${params.toString()}`)
        
        if (!response.ok) {
          throw new Error("Failed to fetch low stock products")
        }
        
        const data = await response.json()
        setProducts(data.products)
      } catch (error) {
        console.error("Error fetching low stock products:", error)
        setError(error instanceof Error ? error.message : "An unknown error occurred")
      } finally {
        setLoading(false)
      }
    }
    
    fetchLowStockProducts()
  }, [searchQuery, sortBy])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
  }

  const handleSortChange = (value: string) => {
    setSortBy(value)
  }

  const handleThresholdUpdate = (product: Product) => {
    setSelectedProduct(product)
    setIsThresholdDialogOpen(true)
  }

  const handleThresholdUpdated = () => {
    // Refresh the product list
    const fetchLowStockProducts = async () => {
      try {
        const params = new URLSearchParams()
        params.append("stockFilter", "low_stock")
        
        if (searchQuery) {
          params.append("query", searchQuery)
        }
        
        if (sortBy) {
          params.append("sort", sortBy)
        }
        
        const response = await fetch(`/api/seller/products?${params.toString()}`)
        
        if (!response.ok) {
          throw new Error("Failed to fetch low stock products")
        }
        
        const data = await response.json()
        setProducts(data.products)
      } catch (error) {
        console.error("Error refreshing low stock products:", error)
      }
    }
    
    fetchLowStockProducts()
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t("error") || "Error"}</CardTitle>
          <CardDescription>{error}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => window.location.reload()}>
            {t("tryAgain") || "Try Again"}
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className="flex flex-col sm:flex-row gap-4 items-end mb-6">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder={t("searchProducts") || "Search products..."}
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </form>
        
        <div className="w-full sm:w-[180px]">
          <Select value={sortBy} onValueChange={handleSortChange}>
            <SelectTrigger>
              <div className="flex items-center">
                <AlertTriangle className="mr-2 h-3.5 w-3.5" />
                <span>{t("sortBy") || "Sort by"}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="stock_low">{t("stockLowToHigh") || "Stock: Low to High"}</SelectItem>
              <SelectItem value="threshold_high">{t("thresholdHighToLow") || "Threshold: High to Low"}</SelectItem>
              <SelectItem value="name_asc">{t("nameAZ") || "Name: A-Z"}</SelectItem>
              <SelectItem value="name_desc">{t("nameZA") || "Name: Z-A"}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-yellow-500" />
              {t("lowStockAlerts") || "Low Stock Alerts"}
            </div>
          </CardTitle>
          <CardDescription>
            {t("lowStockAlertsDesc") || "Products that are below or near their minimum stock threshold."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-md" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-12">
              <div className="flex justify-center mb-4">
                <div className="rounded-full bg-green-100 p-3">
                  <AlertTriangle className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <h3 className="text-lg font-medium mb-2">
                {t("noLowStockProducts") || "No Low Stock Products"}
              </h3>
              <p className="text-gray-500 max-w-md mx-auto">
                {t("allProductsAboveThreshold") || "All your products are above their minimum stock threshold. Great job managing your inventory!"}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("product") || "Product"}</TableHead>
                    <TableHead className="text-center">{t("currentStock") || "Current Stock"}</TableHead>
                    <TableHead className="text-center">{t("threshold") || "Threshold"}</TableHead>
                    <TableHead className="text-center">{t("status") || "Status"}</TableHead>
                    <TableHead className="text-right">{t("actions") || "Actions"}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product) => (
                    <TableRow key={product._id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 relative rounded overflow-hidden bg-gray-100">
                            {product.images && product.images.length > 0 ? (
                              <Image
                                src={product.images[0]}
                                alt={product.title}
                                fill
                                className="object-cover"
                                sizes="40px"
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full w-full text-gray-400 text-xs">
                                No image
                              </div>
                            )}
                          </div>
                          <div className="font-medium truncate max-w-[200px]">
                            {product.title}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge variant={product.stock > 0 ? "outline" : "destructive"}>
                          {product.stock}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-center">
                        {product.minStockThreshold !== undefined ? (
                          <Badge variant="secondary">
                            {product.minStockThreshold}
                          </Badge>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell className="text-center">
                        {product.stock === 0 ? (
                          <Badge variant="destructive">
                            {t("outOfStock") || "Out of Stock"}
                          </Badge>
                        ) : product.minStockThreshold !== undefined && product.stock <= product.minStockThreshold ? (
                          <Badge variant="warning" className="bg-yellow-500">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            {t("lowStock") || "Low Stock"}
                          </Badge>
                        ) : (
                          <Badge variant="success" className="bg-green-500">
                            {t("inStock") || "In Stock"}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="outline" 
                            size="icon"
                            onClick={() => router.push(`/seller/products/edit/${product._id}`)}
                            title={t("editProduct") || "Edit Product"}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="icon"
                            onClick={() => handleThresholdUpdate(product)}
                            title={t("setThreshold") || "Set Threshold"}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            {products.length > 0 && (
              <>{t("showingProducts", { count: products.length }) || `Showing ${products.length} products`}</>
            )}
          </div>
          <Button 
            variant="outline"
            onClick={() => router.push("/seller/products/list")}
          >
            {t("viewAllProducts") || "View All Products"}
          </Button>
        </CardFooter>
      </Card>
      
      {selectedProduct && (
        <ThresholdDialog
          product={selectedProduct}
          open={isThresholdDialogOpen}
          onOpenChange={setIsThresholdDialogOpen}
          onThresholdUpdated={handleThresholdUpdated}
        />
      )}
    </>
  )
}
