"use client"

import { useState } from "react"
import { useTranslation } from "react-i18next"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@components/ui/dialog"
import { Button } from "@components/ui/button"
import { Input } from "@components/ui/input"
import { Label } from "@components/ui/label"
import { useToast } from "@components/ui/use-toast"
import { AlertTriangle, Settings } from "lucide-react"

interface Product {
  _id: string
  title: string
  stock: number
  minStockThreshold?: number
}

interface ThresholdDialogProps {
  product: Product
  open: boolean
  onOpenChange: (open: boolean) => void
  onThresholdUpdated?: () => void
}

export function ThresholdDialog({
  product,
  open,
  onOpenChange,
  onThresholdUpdated,
}: ThresholdDialogProps) {
  const { t } = useTranslation("seller")
  const { toast } = useToast()
  
  const [threshold, setThreshold] = useState<number>(product.minStockThreshold || 5)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleThresholdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value)
    if (!isNaN(value) && value >= 0) {
      setThreshold(value)
      setError(null)
    }
  }

  const handleSubmit = async () => {
    // Validate threshold
    if (threshold < 0) {
      setError(t("thresholdMustBePositive") || "Threshold must be a positive number")
      return
    }

    try {
      setIsSubmitting(true)
      setError(null)

      // Call the threshold update API
      const response = await fetch(`/api/seller/products/${product._id}/threshold`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ minStockThreshold: threshold }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update threshold")
      }

      // Show success message
      toast({
        title: t("thresholdUpdated") || "Threshold Updated",
        description: t("thresholdUpdatedDesc", { product: product.title, threshold }) || 
          `Stock threshold for ${product.title} has been set to ${threshold}.`,
      })

      // Close dialog and notify parent
      onOpenChange(false)
      if (onThresholdUpdated) {
        onThresholdUpdated()
      }
    } catch (error) {
      console.error("Error updating threshold:", error)
      setError(error instanceof Error ? error.message : "An unknown error occurred")
      
      toast({
        title: t("error") || "Error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            <div className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              {t("setStockThreshold") || "Set Stock Threshold"}
            </div>
          </DialogTitle>
          <DialogDescription>
            {t("setStockThresholdDesc", { product: product.title }) || 
              `Set the minimum stock threshold for ${product.title}. You'll be alerted when stock falls below this level.`}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="current-stock">
              {t("currentStock") || "Current Stock"}
            </Label>
            <Input
              id="current-stock"
              value={product.stock}
              disabled
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="threshold">
              {t("minStockThreshold") || "Minimum Stock Threshold"}
            </Label>
            <Input
              id="threshold"
              type="number"
              min={0}
              value={threshold}
              onChange={handleThresholdChange}
            />
            {error && (
              <p className="text-sm text-red-500 mt-1">
                <AlertTriangle className="h-3 w-3 inline-block mr-1" />
                {error}
              </p>
            )}
            <p className="text-sm text-muted-foreground">
              {t("thresholdHelp") || "You'll be alerted when stock falls below this level."}
            </p>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">
                  {t("thresholdInfo") || "About Stock Thresholds"}
                </h4>
                <p className="text-xs text-yellow-700 mt-1">
                  {t("thresholdInfoDesc") || 
                    "Setting a threshold helps you manage inventory more efficiently. Products with stock below threshold will appear in the Low Stock Alerts tab."}
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            {t("cancel") || "Cancel"}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>{t("updating") || "Updating..."}</>
            ) : (
              <>{t("saveThreshold") || "Save Threshold"}</>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
