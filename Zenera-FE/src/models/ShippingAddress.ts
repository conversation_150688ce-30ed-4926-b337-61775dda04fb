import mongoose, { Schema, Document } from 'mongoose';
import type { IUser } from './User';

export interface IShippingAddress extends Document {
  user: IUser['_id'];
  fullName: string;
  phoneNumber: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  district: string;
  ward: string;
  postalCode?: string;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const ShippingAddressSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    fullName: { type: String, required: true },
    phoneNumber: { type: String, required: true },
    addressLine1: { type: String, required: true },
    addressLine2: { type: String },
    city: { type: String, required: true },
    district: { type: String, required: true },
    ward: { type: String, required: true },
    postalCode: { type: String },
    isDefault: { type: Boolean, default: false },
  },
  { timestamps: true }
);

// Đảm bảo mỗi người dùng chỉ có một địa chỉ mặc định
ShippingAddressSchema.pre('save', async function (next) {
  const address = this as IShippingAddress;
  
  // Nếu địa chỉ này được đánh dấu là mặc định
  if (address.isDefault) {
    try {
      // Tìm tất cả các địa chỉ khác của người dùng này đang được đánh dấu là mặc định
      const model = mongoose.model('ShippingAddress');
      await model.updateMany(
        { user: address.user, _id: { $ne: address._id }, isDefault: true },
        { $set: { isDefault: false } }
      );
      next();
    } catch (error) {
      next(error as Error);
    }
  } else {
    next();
  }
});

export default mongoose.models.ShippingAddress || 
  mongoose.model<IShippingAddress>('ShippingAddress', ShippingAddressSchema);
