{"accordion": "Đàn accordion", "addToCart": "Thêm vào giỏ hàng", "badge": "<PERSON><PERSON>", "books": "<PERSON><PERSON><PERSON>", "brandA": "<PERSON><PERSON><PERSON><PERSON><PERSON> hiệu A", "brandB": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>u <PERSON>", "brandC": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>u <PERSON>", "brandD": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>u <PERSON>", "brandE": "<PERSON><PERSON><PERSON><PERSON><PERSON> hiệu <PERSON>", "brands": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "button": "<PERSON><PERSON><PERSON>", "categories": "<PERSON><PERSON><PERSON> lo<PERSON>", "category": "<PERSON><PERSON><PERSON>", "checkbox": "<PERSON><PERSON><PERSON>", "clearAll": "<PERSON><PERSON><PERSON> tất cả", "clothing": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> miêu tả", "electronics": "<PERSON><PERSON><PERSON><PERSON> tử", "exploreProducts": "Khám phá nhiều loại sản phẩm của chúng tôi và tìm chính xác những gì bạn đang tìm kiếm.", "featured": "<PERSON><PERSON><PERSON> b<PERSON>t", "filters": "<PERSON><PERSON> lọc", "findYourPerfectProduct": "<PERSON><PERSON><PERSON> sản phẩm hoàn hảo của bạn", "hideFilters": "Ẩn <PERSON><PERSON> lọc", "highestRated": "<PERSON><PERSON><PERSON> giá cao nhất", "homeGarden": "Nhà <PERSON>ử<PERSON> & Vư<PERSON><PERSON> t<PERSON>", "images": "<PERSON><PERSON><PERSON>", "newestFirst": "<PERSON><PERSON><PERSON> nh<PERSON>t tr<PERSON>c", "noDescription": "<PERSON><PERSON><PERSON>ng có mô tả nào có sẵn", "pagination": "<PERSON><PERSON> trang", "price": "Giá", "priceHighToLow": "Giá: <PERSON>", "priceLowToHigh": "Giá: <PERSON><PERSON><PERSON><PERSON>", "priceRange": "Phạm vi giá", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "productImage": "<PERSON><PERSON><PERSON> sản phẩm", "rating": "<PERSON><PERSON><PERSON> h<PERSON>", "searchBackground": "<PERSON><PERSON><PERSON> kiếm nền tảng", "sheet": "Tờ giấy", "showFilters": "<PERSON><PERSON><PERSON> thị bộ lọc", "showingResults": "<PERSON><PERSON><PERSON> thị {{start}} - {{end}} trong tổng số {{total}} kết quả", "slider": "<PERSON><PERSON>", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "sports": "<PERSON><PERSON><PERSON> thao", "title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}