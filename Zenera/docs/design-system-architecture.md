# Zenera Design System Architecture

## 🎨 Overview

Zenera Design System được thiết kế để tạo ra một **visual identity độc đáo** với animations và styles riêng, đồng thời đảm bảo **consistency** và **scalability** cho toàn bộ hệ thống e-commerce.

## 🎯 Design System Goals

1. **Unique Visual Identity**: Tạo ra style riêng biệt cho Zenera
2. **Scalable Architecture**: Dễ dàng update/thay đổi style cho toàn hệ thống
3. **Performance**: Animations mượt mà, không ảnh hưởng performance
4. **Developer Experience**: Dễ sử dụng và maintain
5. **Consistency**: Đảm bảo UI/UX nhất quán across all features

## 🏗️ Design System Architecture

```
packages/ui/
├── tokens/                     # Design tokens (colors, spacing, etc.)
│   ├── colors.ts              # Color palette
│   ├── typography.ts          # Font scales, weights
│   ├── spacing.ts             # Spacing scale
│   ├── shadows.ts             # Shadow definitions
│   ├── animations.ts          # Animation presets
│   └── breakpoints.ts         # Responsive breakpoints
├── components/                # Base components
│   ├── primitives/           # Atomic components
│   │   ├── Button/           # Button với variants
│   │   ├── Input/            # Input với animations
│   │   ├── Card/             # Card với hover effects
│   │   └── ...
│   ├── compositions/         # Composite components
│   │   ├── ProductCard/      # E-commerce specific
│   │   ├── CartItem/         # Shopping cart item
│   │   ├── OrderSummary/     # Order summary
│   │   └── ...
│   └── layouts/              # Layout components
│       ├── Container/        # Responsive container
│       ├── Grid/             # Grid system
│       └── Stack/            # Flexbox utilities
├── animations/               # Animation system
│   ├── presets/             # Pre-defined animations
│   ├── transitions/         # Transition utilities
│   ├── keyframes/           # Custom keyframes
│   └── hooks/               # Animation hooks
├── themes/                  # Theme system
│   ├── base.ts             # Base theme
│   ├── light.ts            # Light theme
│   ├── dark.ts             # Dark theme (future)
│   └── custom.ts           # Custom theme variants
└── utils/                  # Utility functions
    ├── cn.ts              # Class name utility
    ├── responsive.ts      # Responsive utilities
    └── animation.ts       # Animation utilities
```

## 🎨 Design Tokens System

### **Color System**
```typescript
// packages/ui/tokens/colors.ts
export const colors = {
  // Primary brand colors
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',  // Main brand color
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },
  
  // Secondary colors
  secondary: {
    50: '#f8fafc',
    500: '#64748b',
    900: '#0f172a',
  },
  
  // Semantic colors
  success: {
    50: '#f0fdf4',
    500: '#22c55e',
    900: '#14532d',
  },
  
  warning: {
    50: '#fffbeb',
    500: '#f59e0b',
    900: '#78350f',
  },
  
  error: {
    50: '#fef2f2',
    500: '#ef4444',
    900: '#7f1d1d',
  },
  
  // E-commerce specific
  ecommerce: {
    price: '#059669',      // Green for prices
    discount: '#dc2626',   // Red for discounts
    rating: '#f59e0b',     // Amber for ratings
    inStock: '#22c55e',    // Green for in stock
    outOfStock: '#6b7280', // Gray for out of stock
  },
} as const;
```

### **Typography System**
```typescript
// packages/ui/tokens/typography.ts
export const typography = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'monospace'],
    display: ['Poppins', 'Inter', 'sans-serif'], // For headings
  },
  
  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem' }],
    sm: ['0.875rem', { lineHeight: '1.25rem' }],
    base: ['1rem', { lineHeight: '1.5rem' }],
    lg: ['1.125rem', { lineHeight: '1.75rem' }],
    xl: ['1.25rem', { lineHeight: '1.75rem' }],
    '2xl': ['1.5rem', { lineHeight: '2rem' }],
    '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
    '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
    '5xl': ['3rem', { lineHeight: '1' }],
  },
  
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
} as const;
```

### **Animation System**
```typescript
// packages/ui/tokens/animations.ts
export const animations = {
  // Duration presets
  duration: {
    fast: '150ms',
    normal: '250ms',
    slow: '350ms',
    slower: '500ms',
  },
  
  // Easing functions
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },
  
  // Pre-defined animations
  presets: {
    fadeIn: {
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
    slideUp: {
      from: { transform: 'translateY(10px)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 },
    },
    scaleIn: {
      from: { transform: 'scale(0.95)', opacity: 0 },
      to: { transform: 'scale(1)', opacity: 1 },
    },
    shimmer: {
      '0%': { transform: 'translateX(-100%)' },
      '100%': { transform: 'translateX(100%)' },
    },
  },
} as const;
```

## 🎭 Component Architecture

### **Base Component Pattern**
```typescript
// packages/ui/components/primitives/Button/Button.tsx
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@zenera/ui/utils';

const buttonVariants = cva(
  // Base styles
  'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-250 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        primary: 'bg-primary-500 text-white hover:bg-primary-600 active:bg-primary-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5',
        secondary: 'bg-secondary-100 text-secondary-900 hover:bg-secondary-200 border border-secondary-300',
        ghost: 'hover:bg-secondary-100 text-secondary-700 hover:text-secondary-900',
        destructive: 'bg-error-500 text-white hover:bg-error-600 shadow-lg hover:shadow-xl',
      },
      size: {
        sm: 'h-9 px-3 text-sm',
        md: 'h-11 px-4 text-base',
        lg: 'h-13 px-6 text-lg',
        xl: 'h-15 px-8 text-xl',
      },
      animation: {
        none: '',
        bounce: 'hover:animate-bounce',
        pulse: 'hover:animate-pulse',
        wiggle: 'hover:animate-wiggle',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      animation: 'none',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, animation, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, animation, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
```

### **E-commerce Specific Components**
```typescript
// packages/ui/components/compositions/ProductCard/ProductCard.tsx
export interface ProductCardProps {
  product: {
    id: string;
    name: string;
    price: number;
    originalPrice?: number;
    image: string;
    rating?: number;
    inStock: boolean;
  };
  onAddToCart?: (productId: string) => void;
  variant?: 'default' | 'compact' | 'featured';
}

export const ProductCard = ({ product, onAddToCart, variant = 'default' }: ProductCardProps) => {
  return (
    <Card className={cn(
      'group overflow-hidden transition-all duration-300',
      'hover:shadow-xl hover:-translate-y-1',
      variant === 'featured' && 'border-primary-200 shadow-lg'
    )}>
      {/* Image với hover effect */}
      <div className="relative overflow-hidden">
        <img 
          src={product.image} 
          alt={product.name}
          className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
        />
        
        {/* Overlay với quick actions */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300">
          <div className="absolute bottom-4 left-4 right-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
            <Button 
              variant="primary" 
              size="sm" 
              className="w-full"
              onClick={() => onAddToCart?.(product.id)}
            >
              Add to Cart
            </Button>
          </div>
        </div>
        
        {/* Discount badge */}
        {product.originalPrice && (
          <Badge 
            variant="destructive" 
            className="absolute top-2 right-2 animate-pulse"
          >
            {Math.round((1 - product.price / product.originalPrice) * 100)}% OFF
          </Badge>
        )}
      </div>
      
      {/* Content */}
      <CardContent className="p-4">
        <h3 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
          {product.name}
        </h3>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-2xl font-bold text-ecommerce-price">
              ${product.price}
            </span>
            {product.originalPrice && (
              <span className="text-sm text-gray-500 line-through">
                ${product.originalPrice}
              </span>
            )}
          </div>
          
          {product.rating && (
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 fill-ecommerce-rating text-ecommerce-rating" />
              <span className="text-sm text-gray-600">{product.rating}</span>
            </div>
          )}
        </div>
        
        <div className="mt-2">
          <Badge 
            variant={product.inStock ? 'success' : 'secondary'}
            className="text-xs"
          >
            {product.inStock ? 'In Stock' : 'Out of Stock'}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
};
```

## 🎬 Animation System

### **Custom Animation Hooks**
```typescript
// packages/ui/animations/hooks/useAnimation.ts
export const useAnimation = (trigger: boolean, options?: AnimationOptions) => {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    if (trigger) {
      setIsVisible(true);
    }
  }, [trigger]);
  
  return {
    isVisible,
    className: isVisible ? 'animate-in' : 'animate-out',
  };
};

// Usage in components
const ProductGrid = ({ products }) => {
  const { isVisible, className } = useAnimation(true);
  
  return (
    <div className={cn(
      'grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6',
      className
    )}>
      {products.map((product, index) => (
        <ProductCard 
          key={product.id} 
          product={product}
          style={{ 
            animationDelay: `${index * 100}ms` // Staggered animation
          }}
        />
      ))}
    </div>
  );
};
```

### **Custom CSS Animations**
```css
/* packages/ui/styles/animations.css */
@keyframes wiggle {
  0%, 100% { transform: rotate(-3deg); }
  50% { transform: rotate(3deg); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
}

.animate-wiggle {
  animation: wiggle 1s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}
```

## 🎨 Theme System

### **Theme Provider**
```typescript
// packages/ui/themes/ThemeProvider.tsx
export const ThemeProvider = ({ children, theme = 'light' }) => {
  return (
    <div 
      className={cn('zenera-theme', `zenera-theme--${theme}`)}
      style={{
        '--primary-50': colors.primary[50],
        '--primary-500': colors.primary[500],
        '--primary-900': colors.primary[900],
        // ... other CSS variables
      }}
    >
      {children}
    </div>
  );
};
```

### **Dynamic Theme Updates**
```typescript
// packages/ui/themes/useTheme.ts
export const useTheme = () => {
  const [theme, setTheme] = useState('light');
  
  const updateTheme = (newTheme: string) => {
    setTheme(newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };
  
  const updateColors = (colorOverrides: Partial<typeof colors>) => {
    Object.entries(colorOverrides).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--${key}`, value);
    });
  };
  
  return {
    theme,
    updateTheme,
    updateColors,
  };
};
```

## 📱 Responsive Design System

### **Breakpoint System**
```typescript
// packages/ui/tokens/breakpoints.ts
export const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Responsive utilities
export const responsive = {
  mobile: `@media (max-width: ${breakpoints.md})`,
  tablet: `@media (min-width: ${breakpoints.md}) and (max-width: ${breakpoints.lg})`,
  desktop: `@media (min-width: ${breakpoints.lg})`,
} as const;
```

This design system architecture ensures that Zenera will have a unique, scalable, and maintainable visual identity that can be easily updated across the entire e-commerce platform.
