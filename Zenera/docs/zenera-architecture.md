# Kiến trúc Hệ thống Thương mại Điện tử Zenera - Simplified

## 📋 Tổng quan

Zenera là một hệ thống thương mại điện tử được thiết kế đơn giản để dễ bắt đầu, nh<PERSON>ng có thể mở rộng theo thời gian. Hệ thống sử dụng kiến trúc monorepo với các công nghệ hiện đại và đã được kiểm chứng.

## 🎯 Nguyên tắc Thiết kế

1. **Đơn giản trước tiên**: Bắt đầu với kiến trúc đơn giản, tối ưu khi cần thiết
2. **Dễ mở rộng**: Thiết kế cho phép thêm tính năng mà không cần refactor lớn
3. **T<PERSON>i sử dụng thông minh**: Chỉ tái sử dụng những gì thực sự cần thiết
4. **Developer Experience**: Ưu tiên trải nghiệm phát triển tốt
5. **Performance**: Tối ưu hiệu suất từ đầu

## 📁 Cấu trúc Thư mục Đơn giản

```
zenera/
├── apps/
│   └── web/                        # Main web application (Next.js 14+)
│       ├── app/                    # App Router
│       │   ├── (buyer)/           # Buyer pages (grouped route)
│       │   │   ├── page.tsx       # Homepage
│       │   │   ├── products/      # Product catalog
│       │   │   ├── cart/          # Shopping cart
│       │   │   └── checkout/      # Checkout flow
│       │   ├── (seller)/          # Seller pages (grouped route)
│       │   │   ├── dashboard/     # Seller dashboard
│       │   │   ├── products/      # Product management
│       │   │   ├── orders/        # Order management
│       │   │   └── analytics/     # Sales analytics
│       │   ├── (admin)/           # Admin pages (grouped route)
│       │   │   ├── dashboard/     # Admin dashboard
│       │   │   ├── users/         # User management
│       │   │   ├── products/      # Product moderation
│       │   │   └── settings/      # System settings
│       │   ├── auth/              # Authentication pages
│       │   │   ├── login/
│       │   │   ├── register/
│       │   │   └── forgot-password/
│       │   ├── api/               # API routes
│       │   ├── globals.css
│       │   └── layout.tsx
│       ├── components/            # React components
│       │   ├── ui/               # Base UI components (từ Zenera-FE)
│       │   ├── forms/            # Form components
│       │   ├── layouts/          # Layout components
│       │   ├── buyer/            # Buyer-specific components
│       │   ├── seller/           # Seller-specific components
│       │   └── admin/            # Admin-specific components
│       ├── lib/                  # Utilities và helpers
│       │   ├── api.ts           # API client
│       │   ├── auth.ts          # Auth utilities
│       │   ├── utils.ts         # General utilities
│       │   └── validations.ts   # Zod schemas
│       ├── hooks/               # Custom React hooks
│       ├── store/               # Zustand stores
│       │   ├── auth.ts         # Auth store
│       │   ├── cart.ts         # Cart store
│       │   └── products.ts     # Products store
│       ├── styles/              # Global styles
│       └── types/               # TypeScript types
│
├── packages/
│   ├── api/                     # Backend API (NestJS)
│   │   ├── src/
│   │   │   ├── modules/
│   │   │   │   ├── auth/       # Authentication
│   │   │   │   ├── users/      # User management
│   │   │   │   ├── products/   # Product management
│   │   │   │   ├── orders/     # Order processing
│   │   │   │   └── payments/   # Payment processing
│   │   │   ├── common/         # Shared utilities
│   │   │   └── main.ts
│   │   └── package.json
│   │
│   ├── shared/                  # Shared utilities
│   │   ├── types/              # Shared TypeScript types
│   │   ├── constants/          # Constants
│   │   ├── utils/              # Utility functions
│   │   └── validations/        # Zod schemas
│   │
│   └── ui/                     # Shared UI components
│       ├── components/         # Reusable components
│       ├── hooks/              # Shared hooks
│       └── utils/              # UI utilities
│
├── tools/                      # Development tools
│   ├── eslint-config/         # Shared ESLint config
│   └── tsconfig/              # Shared TypeScript configs
│
├── docs/                      # Documentation
├── package.json               # Root package.json
├── turbo.json                 # Turborepo config
└── pnpm-workspace.yaml        # PNPM workspace config
```

## 🛠️ Technology Stack - Simplified

### Frontend (apps/web)
- **Framework**: Next.js 14+ với App Router
- **UI Library**:
  - Shadcn/ui (primary - từ Zenera-FE)
  - Tailwind CSS cho styling
- **State Management**:
  - Zustand (simple, lightweight)
  - TanStack Query (server state)
- **Forms**:
  - React Hook Form + Zod validation
- **Authentication**:
  - NextAuth.js hoặc simple JWT
- **Styling**:
  - Tailwind CSS (utility-first)
  - CSS modules khi cần thiết

### Backend (packages/api)
- **Framework**: NestJS (từ zen-buy.be)
- **Database**: MongoDB với Mongoose
- **Authentication**: JWT + Refresh tokens
- **Validation**: Class-validator + Zod
- **Documentation**: Swagger/OpenAPI
- **Caching**: Redis (optional, thêm sau)
- **File Upload**: Multer + Local storage (S3 sau)
- **Testing**: Jest + Supertest

### Shared Libraries (packages/shared)
- **Types**: TypeScript definitions
- **Validation**: Zod schemas
- **Utils**: Utility functions
- **Constants**: Shared constants và enums

### Development Tools
- **Monorepo**: Turborepo (simple setup)
- **Package Manager**: pnpm
- **Linting**: ESLint + Prettier
- **Testing**: Vitest + Testing Library
- **CI/CD**: GitHub Actions (basic)
- **Documentation**: README files (Storybook sau)

## 🔄 Kiến trúc Hệ thống - Simplified

### 1. Frontend Architecture

#### Component Organization (Role-based)
```typescript
// Simple component structure by role
apps/web/components/
├── ui/                    # Base components từ Zenera-FE
│   ├── button.tsx
│   ├── input.tsx
│   ├── card.tsx
│   └── ...
├── buyer/                 # Buyer-specific components
│   ├── product-card.tsx
│   ├── shopping-cart.tsx
│   └── checkout-form.tsx
├── seller/                # Seller-specific components
│   ├── product-form.tsx
│   ├── order-list.tsx
│   └── analytics-chart.tsx
└── admin/                 # Admin-specific components
    ├── user-table.tsx
    ├── product-moderation.tsx
    └── system-settings.tsx
```

#### State Management (Zustand)
```typescript
// Auth store
export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isAuthenticated: false,
  login: async (credentials) => {
    const user = await authService.login(credentials);
    set({ user, isAuthenticated: true });
  },
  logout: () => set({ user: null, isAuthenticated: false }),
}));

// Cart store
export const useCartStore = create<CartState>((set, get) => ({
  items: [],
  total: 0,
  addItem: (product) => {
    const items = [...get().items, product];
    set({ items, total: calculateTotal(items) });
  },
  removeItem: (id) => {
    const items = get().items.filter(item => item.id !== id);
    set({ items, total: calculateTotal(items) });
  },
}));
```

#### Form Handling (React Hook Form + Zod)
```typescript
// Product form schema
const productSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  price: z.number().positive("Price must be positive"),
  category: z.string().min(1, "Category is required"),
});

// Product form component
const ProductForm = () => {
  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
  });

  const onSubmit = async (data: ProductFormData) => {
    await createProduct(data);
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      <Input {...form.register("name")} />
      <Textarea {...form.register("description")} />
      <Input type="number" {...form.register("price")} />
      <Button type="submit">Create Product</Button>
    </form>
  );
};
```

### 2. Backend Architecture (Từ zen-buy.be)

#### Simple Module Structure
```typescript
// Product module - simplified
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Product.name, schema: ProductSchema },
    ]),
  ],
  controllers: [ProductController],
  providers: [ProductService],
  exports: [ProductService],
})
export class ProductModule {}
```

#### Service Layer
```typescript
@Injectable()
export class ProductService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<Product>,
  ) {}

  async createProduct(createProductDto: CreateProductDto): Promise<Product> {
    const product = new this.productModel(createProductDto);
    return await product.save();
  }

  async getProducts(query: any): Promise<Product[]> {
    return await this.productModel.find(query).exec();
  }

  async getProductById(id: string): Promise<Product> {
    return await this.productModel.findById(id).exec();
  }
}
```

### 3. API Design - Simple REST

#### Basic API Structure
```
/api/
├── auth/
│   ├── POST /login
│   ├── POST /register
│   └── POST /logout
├── products/
│   ├── GET /products
│   ├── GET /products/:id
│   ├── POST /products
│   └── PUT /products/:id
├── orders/
│   ├── GET /orders
│   ├── POST /orders
│   └── GET /orders/:id
├── users/
│   ├── GET /profile
│   └── PUT /profile
└── upload/
    └── POST /images
```

#### API Response Format
```typescript
// Success response
{
  success: true,
  data: any,
  message?: string
}

// Error response
{
  success: false,
  error: string,
  details?: any
}
```

## 🎨 UI Component Strategy - Simplified

### 1. Component Organization (Flat Structure)
```typescript
// Simple component structure
apps/web/components/ui/
├── button.tsx           # Base button (từ Zenera-FE)
├── input.tsx            # Base input (từ Zenera-FE)
├── card.tsx             # Base card (từ Zenera-FE)
├── badge.tsx            # Base badge (từ Zenera-FE)
├── dialog.tsx           # Modal dialog (từ Zenera-FE)
├── form.tsx             # Form wrapper
├── table.tsx            # Data table
└── ...                  # Other Shadcn/ui components

// Role-specific components
apps/web/components/
├── buyer/
│   ├── product-card.tsx
│   ├── cart-item.tsx
│   └── checkout-form.tsx
├── seller/
│   ├── product-form.tsx
│   ├── order-item.tsx
│   └── dashboard-stats.tsx
└── admin/
    ├── user-row.tsx
    ├── product-moderation.tsx
    └── settings-form.tsx
```

### 2. Styling Strategy (Tailwind CSS)
```typescript
// Tailwind config - simple and clean
module.exports = {
  content: ['./app/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a',
        },
        gray: {
          50: '#f9fafb',
          500: '#6b7280',
          900: '#111827',
        },
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
    },
  },
  plugins: [],
};

// Usage in components
const ProductCard = ({ product }) => (
  <div className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
    <img
      src={product.image}
      alt={product.name}
      className="w-full h-48 object-cover rounded-md mb-4"
    />
    <h3 className="text-lg font-semibold text-gray-900 mb-2">
      {product.name}
    </h3>
    <p className="text-primary-500 font-bold text-xl">
      ${product.price}
    </p>
  </div>
);
```

### 3. Responsive Design (Mobile-first)
```typescript
// Responsive utilities with Tailwind
const ProductGrid = ({ products }) => (
  <div className="
    grid
    grid-cols-1
    sm:grid-cols-2
    md:grid-cols-3
    lg:grid-cols-4
    gap-4
    p-4
  ">
    {products.map(product => (
      <ProductCard key={product.id} product={product} />
    ))}
  </div>
);

// Responsive navigation
const Navigation = () => (
  <nav className="
    flex
    flex-col
    md:flex-row
    items-center
    justify-between
    p-4
    bg-white
    shadow-sm
  ">
    <Logo />
    <div className="hidden md:flex space-x-6">
      <NavLink href="/products">Products</NavLink>
      <NavLink href="/cart">Cart</NavLink>
    </div>
    <MobileMenu className="md:hidden" />
  </nav>
);
```

## 🔐 Authentication & Authorization

### 1. Authentication Flow
```typescript
// JWT Strategy (từ zen-buy.be)
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  async validate(payload: JwtPayload): Promise<User> {
    const user = await this.userService.findById(payload.sub);
    if (!user || !user.isActive) {
      throw new UnauthorizedException();
    }
    return user;
  }
}

// Frontend auth hook (từ Zenera-FE)
export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const login = async (credentials: LoginCredentials) => {
    const response = await authService.login(credentials);
    setUser(response.user);
    localStorage.setItem('token', response.accessToken);
  };

  return { user, login, logout, loading };
}
```

### 2. Role-Based Access Control
```typescript
// Roles enum
export enum Role {
  ADMIN = 'admin',
  SELLER = 'seller',
  BUYER = 'buyer',
  MODERATOR = 'moderator',
}

// Guards
@Injectable()
export class RolesGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredRoles) return true;

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}
```

## 📊 Data Management

### 1. Database Schema Design
```typescript
// Product schema (từ zen-buy.be)
@Schema({ timestamps: true })
export class Product {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true, unique: true })
  slug: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Category' })
  categoryId: Category;

  @Prop([{ type: ProductVariantSchema }])
  variants: ProductVariant[];

  @Prop({ type: Object })
  seo: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
}
```

### 2. Caching Strategy
```typescript
// Redis caching (từ zen-buy.be)
@Injectable()
export class CacheService {
  constructor(@Inject('REDIS_CLIENT') private redis: Redis) {}

  async get<T>(key: string): Promise<T | null> {
    const value = await this.redis.get(key);
    return value ? JSON.parse(value) : null;
  }

  async set(key: string, value: any, ttl = 3600): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }

  async del(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
```

## 🚀 Performance Optimization

### 1. Frontend Optimization
```typescript
// Code splitting
const ProductDetail = lazy(() => import('./ProductDetail'));
const SellerDashboard = lazy(() => import('./SellerDashboard'));

// Image optimization (Next.js)
import Image from 'next/image';

<Image
  src="/product-image.jpg"
  alt="Product"
  width={300}
  height={200}
  priority={isAboveFold}
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>

// Bundle analysis
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});
```

### 2. Backend Optimization
```typescript
// Database indexing
ProductSchema.index({ name: 'text', description: 'text' });
ProductSchema.index({ categoryId: 1, isActive: 1 });
ProductSchema.index({ createdAt: -1 });

// Query optimization
async findProducts(filter: ProductFilter) {
  return this.productModel
    .find(filter)
    .populate('categoryId', 'name slug')
    .select('-__v')
    .lean() // Return plain objects
    .exec();
}

// Response compression
app.use(compression());
```

## 🧪 Testing Strategy

### 1. Frontend Testing
```typescript
// Component testing (từ Zenera-FE patterns)
describe('ProductCard', () => {
  it('should render product information correctly', () => {
    const product = mockProduct();
    render(<ProductCard product={product} />);

    expect(screen.getByText(product.name)).toBeInTheDocument();
    expect(screen.getByText(product.price)).toBeInTheDocument();
  });
});

// Integration testing
describe('Product API', () => {
  it('should fetch products successfully', async () => {
    const { result } = renderHook(() => useProducts());

    await waitFor(() => {
      expect(result.current.data).toBeDefined();
      expect(result.current.isLoading).toBe(false);
    });
  });
});
```

### 2. Backend Testing
```typescript
// Unit testing (từ zen-buy.be)
describe('ProductService', () => {
  it('should create a product successfully', async () => {
    const createProductDto = mockCreateProductDto();
    const result = await service.createProduct(createProductDto);

    expect(result).toBeDefined();
    expect(result.name).toBe(createProductDto.name);
  });
});

// E2E testing
describe('Products (e2e)', () => {
  it('/products (GET)', () => {
    return request(app.getHttpServer())
      .get('/products')
      .expect(200)
      .expect((res) => {
        expect(res.body.products).toBeDefined();
      });
  });
});
```

## 📦 Package Management

### 1. Monorepo Configuration
```json
// package.json (root)
{
  "name": "zenera",
  "private": true,
  "workspaces": [
    "packages/*",
    "apps/*"
  ],
  "scripts": {
    "build": "turbo run build",
    "dev": "turbo run dev --parallel",
    "test": "turbo run test",
    "lint": "turbo run lint"
  },
  "devDependencies": {
    "turbo": "^1.10.0",
    "lerna": "^7.0.0"
  }
}

// turbo.json
{
  "pipeline": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "dist/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    },
    "test": {
      "dependsOn": ["build"]
    }
  }
}
```

### 2. Dependency Management
```json
// packages/webapp/package.json
{
  "name": "@zenera/webapp",
  "dependencies": {
    "@zenera/ui-components": "workspace:*",
    "@zenera/shared": "workspace:*",
    "next": "^14.0.0",
    "react": "^18.0.0"
  }
}

// packages/api-server/package.json
{
  "name": "@zenera/api-server",
  "dependencies": {
    "@zenera/shared": "workspace:*",
    "@nestjs/core": "^10.0.0",
    "@nestjs/common": "^10.0.0"
  }
}
```

## 🔄 Development Workflow

### 1. Git Workflow
```bash
# Feature development
git checkout -b feature/product-management
git add .
git commit -m "feat(products): add product CRUD operations"
git push origin feature/product-management

# Conventional commits
feat: new feature
fix: bug fix
docs: documentation
style: formatting
refactor: code refactoring
test: adding tests
chore: maintenance
```

### 2. CI/CD Pipeline
```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'

      - run: pnpm install
      - run: pnpm run lint
      - run: pnpm run test
      - run: pnpm run build
```

## 🚀 Deployment Strategy

### 1. Environment Configuration
```bash
# Development
NEXT_PUBLIC_API_URL=http://localhost:4000
DATABASE_URL=mongodb://localhost:27017/zenera-dev
REDIS_URL=redis://localhost:6379

# Production
NEXT_PUBLIC_API_URL=https://api.zenera.com
DATABASE_URL=mongodb+srv://user:<EMAIL>/zenera
REDIS_URL=redis://redis.zenera.com:6379
```

### 2. Docker Configuration
```dockerfile
# packages/webapp/Dockerfile
FROM node:18-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM base AS build
COPY . .
RUN npm run build

FROM base AS runtime
COPY --from=build /app/.next ./.next
EXPOSE 3000
CMD ["npm", "start"]
```

### 3. Kubernetes Deployment
```yaml
# k8s/webapp-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zenera-webapp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: zenera-webapp
  template:
    metadata:
      labels:
        app: zenera-webapp
    spec:
      containers:
      - name: webapp
        image: zenera/webapp:latest
        ports:
        - containerPort: 3000
        env:
        - name: NEXT_PUBLIC_API_URL
          value: "https://api.zenera.com"
```

## 📈 Monitoring & Analytics

### 1. Application Monitoring
```typescript
// Error tracking
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
});

// Performance monitoring
import { Analytics } from '@vercel/analytics/react';

export default function App() {
  return (
    <>
      <Component {...pageProps} />
      <Analytics />
    </>
  );
}
```

### 2. Business Analytics
```typescript
// Custom analytics service
export class AnalyticsService {
  trackPurchase(orderId: string, value: number, items: any[]) {
    // Google Analytics 4
    gtag('event', 'purchase', {
      transaction_id: orderId,
      value: value,
      currency: 'USD',
      items: items,
    });

    // Custom analytics
    this.sendEvent('purchase', { orderId, value, items });
  }

  trackProductView(productId: string) {
    gtag('event', 'view_item', {
      currency: 'USD',
      value: product.price,
      items: [product],
    });
  }
}
```

## 🔮 Future Roadmap

### Phase 1: Core E-commerce (3 months)
- [ ] Basic product catalog
- [ ] Shopping cart functionality
- [ ] User authentication
- [ ] Order management
- [ ] Payment integration

### Phase 2: Advanced Features (6 months)
- [ ] Seller dashboard
- [ ] Inventory management
- [ ] Advanced search & filtering
- [ ] Reviews & ratings
- [ ] Wishlist functionality

### Phase 3: Scale & Optimize (9 months)
- [ ] Mobile app (React Native)
- [ ] Advanced analytics
- [ ] AI-powered recommendations
- [ ] Multi-vendor marketplace
- [ ] International expansion

### Phase 4: Innovation (12 months)
- [ ] AR/VR product visualization
- [ ] Voice commerce
- [ ] Blockchain integration
- [ ] IoT integration
- [ ] Advanced AI features

## 📚 Documentation & Resources

### 1. Development Guides
- [Getting Started](./development/getting-started.md)
- [Component Development](./development/components.md)
- [API Development](./development/api.md)
- [Testing Guide](./development/testing.md)

### 2. Architecture Decisions
- [ADR-001: Monorepo Structure](./architecture/adr-001-monorepo.md)
- [ADR-002: Frontend Framework](./architecture/adr-002-frontend.md)
- [ADR-003: Backend Framework](./architecture/adr-003-backend.md)
- [ADR-004: Database Choice](./architecture/adr-004-database.md)

### 3. API Documentation
- [REST API Reference](./api/rest-api.md)
- [GraphQL Schema](./api/graphql.md)
- [Authentication](./api/authentication.md)

## 🤝 Contributing

### 1. Development Setup
```bash
# Clone repository
git clone https://github.com/zenera/zenera.git
cd zenera

# Install dependencies
pnpm install

# Setup environment
cp .env.example .env.local

# Start development servers
pnpm dev
```

### 2. Code Standards
- Follow TypeScript strict mode
- Use ESLint + Prettier for formatting
- Write tests for new features
- Follow conventional commit messages
- Update documentation

### 3. Pull Request Process
1. Create feature branch from `develop`
2. Implement changes with tests
3. Update documentation
4. Submit PR with clear description
5. Address review feedback
6. Merge after approval

## 📋 Phân tích Tái sử dụng - Simplified

### 1. Từ Zenera-FE (Ưu tiên cao)

#### ✅ Tái sử dụng trực tiếp:
- **UI Components**: Shadcn/ui components (button, input, card, dialog, etc.)
- **Styling**: Tailwind CSS configuration và utility classes
- **Form Handling**: React Hook Form patterns (bỏ Hookstate)
- **TypeScript**: Type definitions và interfaces
- **Responsive Design**: Mobile-first approach với Tailwind

#### 🔧 Cần điều chỉnh:
- Bỏ Hookstate, thay bằng Zustand
- Bỏ dark/light theme toggle
- Bỏ các logic phức tạp, chỉ giữ UI components
- Tổ chức lại theo role-based structure

### 2. Từ zen-buy.be (Ưu tiên cao)

#### ✅ Tái sử dụng trực tiếp:
- **NestJS Architecture**: Module structure đơn giản
- **Database Schemas**: Product, Order, User schemas
- **Authentication**: JWT strategy (đơn giản hóa)
- **API Controllers**: Basic CRUD operations
- **Validation**: Class-validator patterns

#### 🔧 Cần điều chỉnh:
- Đơn giản hóa module structure
- Bỏ caching Redis ban đầu (thêm sau)
- Tập trung vào core features trước

### 3. Từ Medoo (Ưu tiên thấp)

#### ❌ Bỏ qua:
- Theme system động (quá phức tạp)
- Ant Design components (dùng Shadcn/ui thay thế)
- Redux state management (dùng Zustand)
- Page-zone pattern (dùng Next.js App Router)
- Form builder HForm (dùng React Hook Form)

#### ✅ Có thể tham khảo:
- Authentication patterns (đơn giản hóa)
- API integration patterns
- Monorepo structure concepts

## 🚀 Migration Strategy - Step by Step

### Phase 1: Foundation (Tuần 1-2)
```bash
# 1. Setup monorepo structure
mkdir zenera
cd zenera
pnpm init
mkdir -p apps/web packages/{api,shared,ui}

# 2. Setup Turborepo
pnpm add -D turbo
# Create turbo.json, package.json configs

# 3. Setup Next.js app
cd apps/web
npx create-next-app@latest . --typescript --tailwind --app

# 4. Setup NestJS API
cd packages/api
npx @nestjs/cli new .
```

### Phase 2: Core Backend (Tuần 3-4)
```bash
# 1. Copy core modules từ zen-buy.be
- Auth module (simplified)
- User module
- Product module (basic)

# 2. Setup database
- MongoDB connection
- Basic schemas
- Simple validation

# 3. Basic API endpoints
- Authentication (login/register)
- Products CRUD
- Users profile
```

### Phase 3: Core Frontend (Tuần 5-6)
```bash
# 1. Copy UI components từ Zenera-FE
- Shadcn/ui components
- Basic layouts
- Form components

# 2. Setup state management
- Zustand stores (auth, cart)
- TanStack Query setup
- API client

# 3. Basic pages
- Homepage
- Product listing
- Authentication pages
```

### Phase 4: Role-based Features (Tuần 7-8)
```bash
# 1. Buyer features
- Product catalog
- Shopping cart
- Basic checkout

# 2. Seller features
- Product management
- Order listing
- Basic dashboard

# 3. Admin features
- User management
- Product moderation
- Basic settings
```

## 🎯 Kết luận và Khuyến nghị

### 1. Kiến trúc Cuối cùng
- **Monorepo**: Turborepo với pnpm workspaces
- **Frontend**: Next.js 14+ + Shadcn/ui + Tailwind CSS + Zustand
- **Backend**: NestJS + MongoDB (đơn giản hóa từ zen-buy.be)
- **Shared**: TypeScript types + Zod validation + utilities

### 2. Nguyên tắc Phát triển
1. **Start Simple**: Bắt đầu với features cơ bản nhất
2. **Iterate Fast**: Deploy sớm, cải thiện dần
3. **Role-based**: Tổ chức code theo từng role rõ ràng
4. **Performance Later**: Tối ưu khi có traffic thực tế

### 3. Key Benefits
- **Dễ bắt đầu**: Kiến trúc đơn giản, ít dependencies
- **Dễ mở rộng**: Có thể thêm features mà không refactor lớn
- **Code Reuse**: Tái sử dụng thông minh từ 3 sources
- **Developer Experience**: Modern tooling, fast development
- **Maintainable**: Clean code structure, good separation

### 4. Next Steps
1. **Tuần 1-2**: Setup project structure
2. **Tuần 3-4**: Basic backend API
3. **Tuần 5-6**: Basic frontend pages
4. **Tuần 7-8**: Role-based features
5. **Tuần 9+**: Advanced features, optimization

---

**Kiến trúc này được thiết kế để dễ bắt đầu và có thể mở rộng theo nhu cầu thực tế.**
