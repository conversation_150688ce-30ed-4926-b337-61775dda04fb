# Kiến trúc <PERSON>era - Tích hợp Medoo Production-tested Components

## 📋 Tổng quan

Zenera là một hệ thống thương mại điện tử được xây dựng dựa trên nền tảng vững chắc từ **Medoo** - một hệ thống đã được kiểm chứng trong production. Hệ thống tận dụng tối đa các thành phần có giá trị cao từ Medoo để đảm bảo chất lượng và giảm 70% thời gian phát triển.

## 🎯 Nguyên tắc Thiết kế

1. **Production-tested First** - Tận dụng tối đa các components đã được kiểm chứng từ Medoo
2. **Enterprise-grade Architecture** - Sử dụng patterns và utilities đã được validate trong production
3. **Smart Integration** - <PERSON><PERSON><PERSON> hợp <PERSON> (backend patterns) + Zenera-FE (UI) + zen-buy.be (domain logic)
4. **Scalable Foundation** - Xây dựng trên nền tảng đã được scale trong thực tế
5. **Developer Experience** - Tận dụng tooling và patterns tốt nhất từ Medoo

## 🏗️ Kiến trúc Tích hợp

```
zenera/
├── apps/
│   └── web/                           # Main web application (Next.js 14+)
│       ├── app/
│       │   ├── [locale]/             # i18n support (từ Medoo)
│       │   │   ├── (buyer)/          # Buyer pages
│       │   │   ├── (seller)/         # Seller pages
│       │   │   └── (admin)/          # Admin pages
│       │   ├── api/                  # API routes
│       │   └── globals.css
│       ├── components/
│       │   ├── ui/                   # Base UI (từ Zenera-FE + Medoo)
│       │   ├── forms/                # Form system (từ Medoo)
│       │   │   ├── h-form/           # Schema-based forms
│       │   │   ├── elements/         # Form elements (HInput, HUpload, etc.)
│       │   │   └── schemas/          # E-commerce form schemas
│       │   ├── buyer/                # Buyer components
│       │   ├── seller/               # Seller components
│       │   └── admin/                # Admin components
│       ├── lib/
│       │   ├── auth/                 # Auth system (từ Medoo)
│       │   │   ├── providers/        # Auth context & providers
│       │   │   ├── hooks/            # Auth hooks
│       │   │   └── utils/            # Auth utilities
│       │   ├── api/                  # API layer (từ Medoo)
│       │   │   ├── http/             # HTTP client
│       │   │   ├── endpoints/        # Endpoint management
│       │   │   └── error-handling/   # Error processing
│       │   ├── i18n/                 # i18n system (từ Medoo)
│       │   │   ├── hooks/            # Translation hooks
│       │   │   └── config/           # i18n configuration
│       │   ├── utils/                # Common utilities (từ Medoo)
│       │   └── hooks/                # Common hooks (từ Medoo)
│       ├── store/                    # State management
│       │   ├── auth.ts              # Auth store (Zustand + Medoo patterns)
│       │   ├── cart.ts              # Cart store
│       │   └── products.ts          # Products store
│       └── styles/                   # Styling
├── packages/
│   ├── api/                         # Backend API (NestJS từ zen-buy.be)
│   ├── shared/                      # Shared utilities
│   │   ├── types/                   # TypeScript types
│   │   ├── constants/               # Constants (từ Medoo)
│   │   ├── utils/                   # Shared utilities (từ Medoo)
│   │   └── validations/             # Zod schemas
│   └── ui/                          # Shared UI components
└── tools/                           # Development tools
```

## 🛠️ Technology Stack - Production-tested

### Frontend (Medoo + Zenera-FE Integration)
- **Framework**: Next.js 14+ với App Router
- **UI Library**:
  - **Shadcn/ui** (từ Zenera-FE) - Modern, accessible components
  - **Medoo Form System** - Schema-based dynamic forms
- **Styling**:
  - **Tailwind CSS** (từ Zenera-FE) - Utility-first CSS
  - **Medoo Theme System** (simplified) - Theme management
- **State Management**:
  - **Zustand** - Simple state management
  - **Medoo Auth Patterns** - Production-tested auth flows
- **Forms**:
  - **Medoo HForm System** - Schema-driven form generation
  - **React Hook Form + Zod** - Type-safe validation
- **i18n**:
  - **Medoo i18n System** - Multi-language support với SSR
- **API Layer**:
  - **Medoo HTTP Client** - Production-tested API layer
  - **TanStack Query** - Server state management

### Backend (zen-buy.be + Medoo Patterns)
- **Framework**: NestJS (từ zen-buy.be)
- **Database**: MongoDB với Mongoose
- **Authentication**: JWT + Refresh tokens (Medoo patterns)
- **Validation**: Class-validator + Zod
- **Documentation**: Swagger/OpenAPI

### Shared Libraries (Medoo Utilities)
- **Types**: TypeScript definitions
- **Validation**: Zod schemas + Medoo validation utilities
- **Utils**: Production-tested utility functions từ Medoo
- **Constants**: Shared constants và permissions

## 🔥 Core Components từ Medoo (TIER 1 - Critical)

### 1. Authentication System ⭐⭐⭐⭐⭐

#### Extracted từ Medoo:
```typescript
// Auth Provider (adapted cho Zustand)
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      userPermissions: [],
      roles: ['buyer', 'seller', 'admin'],

      // Sử dụng Medoo's authentication logic
      login: async (credentials) => {
        const response = await authAPI.login(credentials);
        const { user, accessToken, refreshToken, permissions } = response;

        // Sử dụng Medoo's cookie management
        HCookiesMethod.setH2Token(accessToken);
        HCookiesMethod.setRefreshToken(refreshToken);
        HCookiesMethod.setH2User(user);

        set({
          user,
          token: accessToken,
          refreshToken,
          isAuthenticated: true,
          userPermissions: permissions,
          roles: user.roles || [],
        });
      },

      logout: () => {
        HCookiesMethod.clearAuthCookies();
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          userPermissions: [],
          roles: [],
        });
      },

      hasRole: (role) => get().roles.includes(role),
      hasPermission: (permission) => get().userPermissions.includes(permission),
    }),
    { name: 'auth-storage' }
  )
);
```

#### E-commerce Permissions:
```typescript
// E-commerce specific permissions
export const ECOMMERCE_PERMISSIONS = {
  // Buyer permissions
  BUYER_VIEW_PRODUCTS: 'BUYER_VIEW_PRODUCTS',
  BUYER_MANAGE_CART: 'BUYER_MANAGE_CART',
  BUYER_PLACE_ORDER: 'BUYER_PLACE_ORDER',

  // Seller permissions
  SELLER_MANAGE_PRODUCTS: 'SELLER_MANAGE_PRODUCTS',
  SELLER_VIEW_ORDERS: 'SELLER_VIEW_ORDERS',
  SELLER_MANAGE_INVENTORY: 'SELLER_MANAGE_INVENTORY',

  // Admin permissions
  ADMIN_MANAGE_USERS: 'ADMIN_MANAGE_USERS',
  ADMIN_MODERATE_PRODUCTS: 'ADMIN_MODERATE_PRODUCTS',
  ADMIN_VIEW_ANALYTICS: 'ADMIN_VIEW_ANALYTICS',
};
```

### 2. Schema-Form System ⭐⭐⭐⭐⭐

#### Medoo HForm + Shadcn/ui Adapter:
```typescript
// Component mapping từ Ant Design sang Shadcn/ui
const COMPONENT_MAP = {
  'Input': Input,           // Shadcn/ui Input
  'Select': Select,         // Shadcn/ui Select
  'Textarea': Textarea,     // Shadcn/ui Textarea
  'Upload': FileUpload,     // Custom upload component
  'Editor': RichTextEditor, // Custom editor component
};

// Adapter function
export const adaptSchemaForShadcn = (schema: HFormItemProps[]): HFormItemProps[] => {
  return schema.map(item => {
    const { Component, componentProps, ...rest } = item;
    const ShadcnComponent = COMPONENT_MAP[Component?.name] || Component;
    const adaptedProps = adaptPropsForShadcn(componentProps);

    return {
      ...rest,
      Component: ShadcnComponent,
      componentProps: adaptedProps,
    };
  });
};
```

#### E-commerce Form Schemas:
```typescript
// Product form schema
export const useProductFormSchema = () => {
  const { t } = useHTranslation('ecommerce');

  return [
    createSchemaItem({
      Component: Input,
      name: 'name',
      label: t('product.name'),
      rules: [{ required: true, message: t('validation.required') }],
    }),
    createSchemaItem({
      Component: Textarea,
      name: 'description',
      label: t('product.description'),
    }),
    createSchemaItem({
      Component: Input,
      name: 'price',
      label: t('product.price'),
      rules: [
        { required: true, message: t('validation.required') },
        { pattern: /^\d+(\.\d{1,2})?$/, message: t('validation.price') }
      ],
      componentProps: { type: 'number', min: 0, step: 0.01 },
    }),
    // File upload từ Medoo
    createSchemaItem({
      Component: HUpload,
      name: 'images',
      label: t('product.images'),
      componentProps: {
        maxFiles: 5,
        accept: 'image/*',
        multiple: true,
        crop: true, // Medoo's image crop feature
      },
    }),
  ];
};
```

### 3. i18n System ⭐⭐⭐⭐⭐

#### Medoo i18n với E-commerce Support:
```typescript
// E-commerce translations
// locales/en/ecommerce.json
{
  "product": {
    "name": "Product Name",
    "description": "Description",
    "price": "Price",
    "category": "Category",
    "images": "Product Images"
  },
  "cart": {
    "addToCart": "Add to Cart",
    "removeFromCart": "Remove",
    "updateQuantity": "Update Quantity",
    "total": "Total",
    "checkout": "Checkout"
  },
  "order": {
    "orderNumber": "Order #",
    "status": "Status",
    "total": "Total Amount",
    "createdAt": "Order Date"
  }
}

// Usage với Medoo's useHTranslation
const ProductCard = ({ product }) => {
  const { t } = useHTranslation('ecommerce');

  return (
    <Card>
      <h3>{product.name}</h3>
      <p>{t('product.price')}: ${product.price}</p>
      <Button>{t('cart.addToCart')}</Button>
    </Card>
  );
};
```

### 4. API Layer ⭐⭐⭐⭐⭐

#### Medoo HTTP Client + React Query:
```typescript
// API client sử dụng Medoo's HTTP layer
class EcommerceAPI {
  // Products
  async getProducts(params?: any) {
    return httpRequester.getDataFromApi('/products', params);
  }

  async createProduct(data: any) {
    return httpRequester.postToApi('/products', data);
  }

  // Orders
  async getOrders(params?: any) {
    return httpRequester.getDataFromApi('/orders', params);
  }

  async createOrder(data: any) {
    return httpRequester.postToApi('/orders', data);
  }

  // Cart
  async getCart() {
    return httpRequester.getDataFromApi('/cart');
  }

  async updateCart(data: any) {
    return httpRequester.putToApi('/cart', data);
  }
}

// React Query hooks
export const useProducts = (params?: any) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => ecommerceAPI.getProducts(params),
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ecommerceAPI.createProduct,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};
```

### 5. Common Utilities ⭐⭐⭐⭐⭐

#### Production-tested Utilities từ Medoo:
```typescript
// E-commerce specific utilities sử dụng Medoo's base utilities
export class EcommerceUtils {
  // Sử dụng Medoo's currency utilities
  static formatPrice(price: number, currency = 'USD'): string {
    return CurrencyUtils.format(price, currency);
  }

  // Sử dụng Medoo's validation utilities
  static validateProductData(product: any) {
    return ValidationUtils.validate(product, productValidationSchema);
  }

  // Sử dụng Medoo's image utilities
  static processProductImages(images: File[]) {
    return ImageUtils.processMultiple(images, {
      maxWidth: 800,
      maxHeight: 600,
      quality: 0.8,
    });
  }

  // Sử dụng Medoo's formatter utilities
  static formatProductSKU(productName: string): string {
    return FormatterUtils.generateSKU(productName);
  }
}
```

## 🔶 TIER 2 Components từ Medoo (Important)

### 6. Common Hooks ⭐⭐⭐⭐

#### Responsive & Mobile Detection:
```typescript
// Sử dụng Medoo's responsive hooks
export const useEcommerceLayout = () => {
  const isMobile = useMobileDetect();
  const isTablet = useMedia('(max-width: 1024px)');

  return {
    isMobile,
    isTablet,
    isDesktop: !isMobile && !isTablet,
    // E-commerce specific layout logic
    showSidebar: !isMobile,
    gridCols: isMobile ? 1 : isTablet ? 2 : 4,
  };
};

// Data fetching với Medoo patterns
export const useProductData = (productId: string) => {
  return useDataWithFetching({
    endpoint: `/products/${productId}`,
    dataNamespace: 'product',
    fetchFlag: !!productId,
  });
};
```

### 7. Layout Components ⭐⭐⭐⭐

#### E-commerce Layouts sử dụng Medoo patterns:
```typescript
// Buyer layout
export const BuyerLayout = ({ children }) => {
  const { user } = useAuthStore();
  const { t } = useHTranslation('ecommerce');

  return (
    <div className="min-h-screen bg-gray-50">
      <Header>
        <Navigation items={buyerNavItems} />
        <CartIcon />
        <UserMenu user={user} />
      </Header>
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
      <Footer />
    </div>
  );
};

// Seller layout với Medoo's drawer pattern
export const SellerLayout = ({ children }) => {
  const { user, hasPermission } = useAuthStore();
  const { t } = useHTranslation('ecommerce');

  return (
    <DrawerPageLayout
      sidebarItems={sellerSidebarItems}
      user={user}
      hasPermission={hasPermission}
    >
      {children}
    </DrawerPageLayout>
  );
};
```

### 8. Theme System (Simplified) ⭐⭐⭐

#### Medoo Theme + Tailwind Integration:
```typescript
// Simplified theme store
export const useThemeStore = create<ThemeState>((set) => ({
  theme: 'light',
  primaryColor: '#3b82f6',

  setTheme: (theme) => set({ theme }),
  setPrimaryColor: (color) => set({ primaryColor: color }),
}));

// Theme provider kết hợp Medoo + Tailwind
export const ThemeProvider = ({ children }) => {
  const { theme, primaryColor } = useThemeStore();

  return (
    <div
      className={theme}
      style={{ '--primary-color': primaryColor }}
    >
      {children}
    </div>
  );
};
```

## 🚀 Migration Strategy - Tích hợp Medoo

### Phase 1: Core Infrastructure (Week 1-2)
```bash
# 1. Extract Authentication System từ Medoo
mkdir -p apps/web/lib/auth/{providers,hooks,utils}
cp -r medoo/packages/webapp/lib/providers/auth* apps/web/lib/auth/providers/
cp -r medoo/packages/webapp/lib/providers/auth-hook/* apps/web/lib/auth/hooks/
cp -r medoo/packages/webapp/lib/utils/authentication* apps/web/lib/auth/utils/

# 2. Extract i18n System từ Medoo
mkdir -p apps/web/lib/i18n/{hooks,config}
cp -r medoo/packages/webapp/lib/i18n/* apps/web/lib/i18n/hooks/
cp -r medoo/packages/webapp/app/i18n/* apps/web/lib/i18n/config/

# 3. Extract API Layer từ Medoo
mkdir -p apps/web/lib/api/{http,endpoints,error-handling}
cp -r medoo/packages/webapp/lib/networks/* apps/web/lib/api/
```

### Phase 2: Form System (Week 3-4)
```bash
# 4. Extract Schema-form System từ Medoo
mkdir -p apps/web/components/forms/{h-form,elements,schemas}
cp -r medoo/packages/webapp/schema-form/* apps/web/components/forms/h-form/
cp -r medoo/packages/webapp/components/shared/common-form-elements/* apps/web/components/forms/elements/

# 5. Create Shadcn/ui Adapters
# Tạo adapter layer để map Ant Design components sang Shadcn/ui
```

### Phase 3: UI Components (Week 5-6)
```bash
# 6. Copy UI components từ Zenera-FE
mkdir -p apps/web/components/ui
cp -r zenera-fe/src/components/ui/* apps/web/components/ui/

# 7. Extract Layout components từ Medoo
mkdir -p apps/web/components/layouts
cp -r medoo/packages/webapp/components/shared/layout/* apps/web/components/layouts/
```

### Phase 4: E-commerce Features (Week 7-8)
```bash
# 8. Implement E-commerce specific components
mkdir -p apps/web/components/{buyer,seller,admin}

# 9. Create E-commerce form schemas
# Sử dụng Medoo's schema system cho product, order, user forms

# 10. Setup role-based routing
# Sử dụng Medoo's permission system cho role-based access
```

## 📊 Expected Value từ Medoo Integration

### Development Time Savings:
- **Authentication System**: 4 weeks → 1 week (75% savings)
- **Schema-form System**: 5 weeks → 1.5 weeks (70% savings)
- **i18n System**: 2 weeks → 3 days (85% savings)
- **API Layer**: 3 weeks → 1 week (67% savings)
- **Common Utilities**: 4 weeks → 1 week (75% savings)

**Total Estimated Savings: 18 weeks → 5.5 weeks (69% time reduction)**

### Quality Benefits:
- ✅ **Production-tested**: Tất cả components đã được test trong production
- ✅ **Bug-free**: Bugs đã được fix qua thời gian sử dụng thực tế
- ✅ **Performance**: Đã được optimize cho production workload
- ✅ **Accessibility**: Hỗ trợ accessibility standards
- ✅ **TypeScript**: Full type coverage và type safety
- ✅ **Enterprise-grade**: Patterns đã được validate trong enterprise environment

### Risk Mitigation:
- ✅ **Proven Architecture**: Patterns đã được validate trong production
- ✅ **Team Knowledge**: Team đã familiar với Medoo codebase
- ✅ **Documentation**: Có examples và usage patterns từ Medoo
- ✅ **Maintenance**: Easier maintenance với familiar code patterns

## 🎯 Kết luận

Việc tích hợp Medoo vào Zenera mang lại:

1. **Chất lượng vượt trội**: Sử dụng components đã được kiểm chứng trong production
2. **Tiết kiệm thời gian**: Giảm 69% thời gian phát triển
3. **Giảm rủi ro**: Sử dụng patterns đã được validate
4. **Scalability**: Architecture đã được test với traffic thực tế
5. **Developer Experience**: Tooling và patterns tốt nhất từ Medoo

Kiến trúc này đảm bảo Zenera được xây dựng trên nền tảng vững chắc và có thể scale theo nhu cầu kinh doanh.