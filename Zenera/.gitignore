# IDE and Editor files
.idea/
.vscode/
*.swp
*.swo
*~

# Dependencies
node_modules/
packages/**/node_modules/
.yarn_cache/
.pnp
.pnp.js

# Build outputs
build/
dist/
packages/**/build/
packages/**/dist/
packages/**/.next/
packages/api-server/dist/
packages/sharing/dist/
packages/telegram-bot/dist/
packages/telegram-bot/dev-dist/
packages/webapp/build/

# TypeScript
*.tsbuildinfo
packages/api-server/tsconfig.tsbuildinfo
packages/api-server/tsconfig.build.*
packages/api-server/tsconfig.build.tsbuildinfo

# Logs
*.log
yarn-error.log
lerna-debug.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Lock files (keep yarn.lock, ignore others)
package-lock.json
packages/**/package-lock.json

# Environment files
.env
.env.local
.env.development
.env.test
.env.production
packages/api-server/.env*
!packages/api-server/.env.example

# Database files
dump.rdb
appendonly.aof
/data

# Docker files (if not needed in repo)
docker-compose.yml
mongo/Dockerfile
mongo/mongo-init.sh

# Generated files
src/**/*.css
*.js.map
*.orig
tags

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Temporary files
tmp/
temp/
*.tmp

# Config files with sensitive data
packages/api-server/configs/gcs-account.json
packages/api-server/public/*

# Test reports
packages/api-server/src/cucumber/reports/*
!packages/api-server/src/cucumber/reports/.gitkeep

# Deployment
convox.yml
.*-current-release

# Ruby
.byebug_history

# Misc
packages/webapp/~
packages/api-server/~
packages/*/packages/sharing
package-no-lerna.json
