# Zenera E-commerce Platform - Simplified

Zenera là một hệ thống thương mại điện tử được thiết kế đơn giản để dễ bắt đầu, nhưng có thể mở rộng theo thời gian. Hệ thống tái sử dụng thông minh các thành phần từ 3 source code hiện có.

## 🎯 Nguyên tắc Thiết kế

1. **Đơn giản trước tiên** - Bắt đầu với kiến trúc đơn giản, tối ưu khi cần thiết
2. **Dễ mở rộng** - Thiết kế cho phép thêm tính năng mà không cần refactor lớn
3. **Tái sử dụng thông minh** - Chỉ tái sử dụng những gì thực sự cần thiết
4. **Role-based Organization** - Tổ chức code theo từng role rõ ràng

## 🏗️ Kiến trúc Đơn giản

```
zenera/
├── apps/
│   └── web/                    # Main web application (Next.js 14+)
│       ├── app/
│       │   ├── (buyer)/       # Buyer pages (grouped route)
│       │   ├── (seller)/      # Seller pages (grouped route)
│       │   ├── (admin)/       # Admin pages (grouped route)
│       │   └── auth/          # Authentication pages
│       ├── components/
│       │   ├── ui/           # Base UI components (từ Zenera-FE)
│       │   ├── buyer/        # Buyer-specific components
│       │   ├── seller/       # Seller-specific components
│       │   └── admin/        # Admin-specific components
│       ├── lib/              # Utilities và helpers
│       ├── hooks/            # Custom React hooks
│       └── store/            # Zustand stores
├── packages/
│   ├── api/                  # Backend API (NestJS)
│   ├── shared/               # Shared utilities
│   └── ui/                   # Shared UI components
└── tools/                    # Development tools
```

## 🛠️ Technology Stack - Simplified

### Frontend
- **Next.js 14+** với App Router
- **Shadcn/ui** (primary UI library)
- **Tailwind CSS** cho styling
- **Zustand** (state management)
- **TanStack Query** (server state)
- **React Hook Form + Zod** validation

### Backend
- **NestJS** framework (từ zen-buy.be)
- **MongoDB** với Mongoose
- **JWT** authentication
- **Class-validator + Zod**
- **Swagger** documentation

### Development
- **Turborepo** (simple setup)
- **pnpm** package manager
- **TypeScript** strict mode
- **Vitest** + **Testing Library**
- **ESLint** + **Prettier**

## 📋 Tính năng theo Role

### 👥 Buyer Features
- Product browsing & search
- Shopping cart & checkout
- Order tracking
- User profile management

### 🏪 Seller Features
- Product management
- Order processing
- Sales analytics
- Inventory tracking

### 👨‍💼 Admin Features
- User management
- Product moderation
- System settings
- Platform analytics

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB
- pnpm

### Installation
```bash
# Clone repository
git clone https://github.com/zenera/zenera.git
cd zenera

# Install dependencies
pnpm install

# Setup environment
cp .env.example .env.local

# Start development servers
pnpm dev
```

### Development Commands
```bash
# Start all packages in development mode
pnpm dev

# Build all packages
pnpm build

# Run tests
pnpm test

# Lint code
pnpm lint
```

## 📚 Documentation

- [Architecture Overview](./docs/zenera-architecture.md) - Simplified system architecture
- [Reusable Components Analysis](./docs/reusable-components-analysis.md) - Component reuse strategy

## 🚀 Migration Strategy - Step by Step

### Phase 1: Foundation (Week 1-2)
- [x] Setup monorepo structure
- [x] Create basic packages
- [ ] Setup Turborepo configuration
- [ ] Basic Next.js app setup

### Phase 2: Core Backend (Week 3-4)
- [ ] Copy core modules from zen-buy.be
- [ ] Setup MongoDB connection
- [ ] Basic authentication
- [ ] Product CRUD APIs

### Phase 3: Core Frontend (Week 5-6)
- [ ] Copy UI components from Zenera-FE
- [ ] Setup Zustand stores
- [ ] Basic pages (home, products, auth)
- [ ] Role-based routing

### Phase 4: Role Features (Week 7-8)
- [ ] Buyer features (catalog, cart, checkout)
- [ ] Seller features (product management, orders)
- [ ] Admin features (user management, moderation)
- [ ] Basic testing

## 🎯 Key Benefits

### Simplified Architecture
- **Easy to start**: Minimal dependencies, clear structure
- **Easy to scale**: Add features without major refactoring
- **Role-based**: Clear separation of concerns

### Smart Code Reuse
- **Zenera-FE**: UI components (Shadcn/ui)
- **zen-buy.be**: Backend modules (simplified)
- **Medoo**: Authentication patterns (simplified)

### Developer Experience
- **Modern tooling**: Turborepo, TypeScript, Tailwind
- **Fast development**: Hot reload, good DX
- **Maintainable**: Clean code structure

## 📊 Project Status

| Component | Source | Status | Priority |
|-----------|--------|--------|----------|
| UI Components | Zenera-FE | 🟡 Ready | High |
| Backend API | zen-buy.be | 🟡 Ready | High |
| Auth System | Medoo | 🔴 Simplify | Medium |
| State Management | New (Zustand) | 🔴 New | High |
| Styling | Zenera-FE | 🟡 Ready | High |

## 📞 Support

- **Documentation**: [./docs/](./docs/)
- **Architecture**: [./docs/zenera-architecture.md](./docs/zenera-architecture.md)

## 📄 License

This project is licensed under the MIT License.

---

**Built with simplicity and scalability in mind 🚀**
