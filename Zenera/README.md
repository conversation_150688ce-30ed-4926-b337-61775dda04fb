# Zenera E-commerce Platform - Powered by Medoo

Zenera là một hệ thống thương mại điện tử được xây dựng dựa trên nền tảng vững chắc từ **Medoo** - một hệ thống đã được kiểm chứng trong production. Hệ thống tận dụng tối đa các thành phần có giá trị cao từ Medoo để đảm bảo chất lượng enterprise-grade và giảm 69% thời gian phát triển.

## 🎯 Nguyên tắc Thiết kế

1. **Production-tested First** - Tận dụng tối đa các components đã được kiểm chứng từ Medoo
2. **Enterprise-grade Quality** - Sử dụng patterns và utilities đã được validate trong production
3. **Smart Integration** - Kết hợp Medoo (core systems) + Zenera-FE (UI) + zen-buy.be (domain logic)
4. **Scalable Foundation** - Xây dựng trên nền tảng đã được scale trong thực tế

## 🏗️ Kiến trúc Đơn giản

```
zenera/
├── apps/
│   └── web/                    # Main web application (Next.js 14+)
│       ├── app/
│       │   ├── (buyer)/       # Buyer pages (grouped route)
│       │   ├── (seller)/      # Seller pages (grouped route)
│       │   ├── (admin)/       # Admin pages (grouped route)
│       │   └── auth/          # Authentication pages
│       ├── components/
│       │   ├── ui/           # Base UI components (từ Zenera-FE)
│       │   ├── buyer/        # Buyer-specific components
│       │   ├── seller/       # Seller-specific components
│       │   └── admin/        # Admin-specific components
│       ├── lib/              # Utilities và helpers
│       ├── hooks/            # Custom React hooks
│       └── store/            # Zustand stores
├── packages/
│   ├── api/                  # Backend API (NestJS)
│   ├── shared/               # Shared utilities
│   └── ui/                   # Shared UI components
└── tools/                    # Development tools
```

## 🛠️ Technology Stack - Production-tested

### Frontend (Medoo + Zenera-FE Integration)
- **Next.js 14+** với App Router
- **Medoo Schema-Form System** - Dynamic form generation đã được kiểm chứng
- **Shadcn/ui** (từ Zenera-FE) - Modern, accessible components
- **Medoo i18n System** - Multi-language support với SSR
- **Tailwind CSS** cho styling
- **Zustand + Medoo Auth Patterns** - Production-tested auth flows
- **Medoo HTTP Client + TanStack Query** - Enterprise-grade API layer

### Backend (zen-buy.be + Medoo Patterns)
- **NestJS** framework (từ zen-buy.be)
- **MongoDB** với Mongoose
- **JWT + Refresh tokens** (Medoo patterns)
- **Class-validator + Zod**
- **Swagger** documentation

### Shared Libraries (Medoo Utilities)
- **Medoo Common Utilities** - Production-tested utility functions
- **Medoo Validation System** - Comprehensive validation
- **TypeScript** strict mode
- **Zod schemas** + Medoo validation patterns

### Development
- **Turborepo** monorepo management
- **pnpm** package manager
- **ESLint** + **Prettier**
- **Vitest** + **Testing Library**

## 📋 Tính năng theo Role

### 👥 Buyer Features
- Product browsing & search
- Shopping cart & checkout
- Order tracking
- User profile management

### 🏪 Seller Features
- Product management
- Order processing
- Sales analytics
- Inventory tracking

### 👨‍💼 Admin Features
- User management
- Product moderation
- System settings
- Platform analytics

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB
- pnpm

### Installation
```bash
# Clone repository
git clone https://github.com/zenera/zenera.git
cd zenera

# Install dependencies
pnpm install

# Setup environment
cp .env.example .env.local

# Start development servers
pnpm dev
```

### Development Commands
```bash
# Start all packages in development mode
pnpm dev

# Build all packages
pnpm build

# Run tests
pnpm test

# Lint code
pnpm lint
```

## 📚 Documentation

- [Architecture Overview](./docs/zenera-architecture.md) - Simplified system architecture
- [Reusable Components Analysis](./docs/reusable-components-analysis.md) - Component reuse strategy

## 🚀 Migration Strategy - Medoo Integration

### Phase 1: Core Infrastructure (Week 1-2)
- [x] Setup monorepo structure
- [x] Create architecture documentation
- [ ] Extract Medoo Authentication System
- [ ] Extract Medoo i18n System
- [ ] Extract Medoo API Layer

### Phase 2: Form System (Week 3-4)
- [ ] Extract Medoo Schema-Form System
- [ ] Create Shadcn/ui Adapters
- [ ] Build E-commerce Form Schemas
- [ ] Integrate with validation system

### Phase 3: UI Components (Week 5-6)
- [ ] Copy UI components from Zenera-FE
- [ ] Extract Medoo Layout Components
- [ ] Setup role-based routing
- [ ] Integrate theme system

### Phase 4: E-commerce Features (Week 7-8)
- [ ] Buyer features (catalog, cart, checkout)
- [ ] Seller features (product management, orders)
- [ ] Admin features (user management, moderation)
- [ ] Production testing

## 🎯 Key Benefits

### Production-tested Quality
- **Enterprise-grade**: Components đã được validate trong production
- **Bug-free**: Bugs đã được fix qua thời gian sử dụng thực tế
- **Performance**: Đã được optimize cho production workload
- **Accessibility**: Hỗ trợ accessibility standards

### Massive Time Savings
- **Authentication System**: 4 weeks → 1 week (75% savings)
- **Schema-form System**: 5 weeks → 1.5 weeks (70% savings)
- **i18n System**: 2 weeks → 3 days (85% savings)
- **API Layer**: 3 weeks → 1 week (67% savings)
- **Total**: 18 weeks → 5.5 weeks (69% time reduction)

### Risk Mitigation
- **Proven Architecture**: Patterns đã được validate trong production
- **Team Knowledge**: Team đã familiar với Medoo codebase
- **Maintenance**: Easier maintenance với familiar code patterns

## 📊 Project Status

| Component | Source | Status | Priority | Value |
|-----------|--------|--------|----------|-------|
| **Auth System** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **Schema-Form System** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **i18n System** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **API Layer** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **Common Utilities** | Medoo | 🟢 Production-tested | Critical | ⭐⭐⭐⭐⭐ |
| **UI Components** | Zenera-FE | 🟡 Ready | High | ⭐⭐⭐⭐ |
| **Backend API** | zen-buy.be | 🟡 Ready | High | ⭐⭐⭐⭐ |
| **Layout Components** | Medoo | 🟡 Ready | Medium | ⭐⭐⭐ |

## 📞 Support

- **Documentation**: [./docs/](./docs/)
- **Architecture with Medoo**: [./docs/zenera-architecture-with-medoo.md](./docs/zenera-architecture-with-medoo.md)
- **Medoo Components Analysis**: [./docs/medoo-extract.md](./docs/medoo-extract.md)
- **Reusable Components**: [./docs/reusable-components-analysis.md](./docs/reusable-components-analysis.md)

## 📄 License

This project is licensed under the MIT License.

---

**Built on the solid foundation of Medoo - Production-tested, Enterprise-grade 🚀**
